{"@@locale": "en", "appName": "Employee Bank", "appSubtitle": "Banking Employee Management System", "login": "<PERSON><PERSON>", "rememberMe": "Remember Me", "uncheckrememberme": "Uncheck 'Remember Me' because biometric is enabled", "validemail": "Please enter a valid email", "correctpassword": "Password must be at least 6 characters", "securityInformation": "Security Information", "rememberMeInfo": "When you enable this option, your login credentials will be securely stored on your device to make future logins easier.\n\nYou can disable this option at any time from the app settings.", "understood": "Understood", "identification": "Identification", "directmanager": "Direct Manager", "quickactions": "Quick Actions", "myprofile": "My Profile", "leaveTypes": "Leave Types", "myLeaveRequests": "My Requests", "leaveApprovals": "Approvals", "retry": "Retry", "settings": "Settings", "appSettings": "App Settings", "customizeSettings": "Customize app settings according to your needs", "accountSettings": "Account <PERSON><PERSON>", "changePassword": "Change Password", "changePasswordSubtitle": "Update your password", "securitySettings": "Security Settings", "biometricLogin": "Biometric Login", "biometricEnabledSubtitle": "Enabled - You can login with biometric", "biometricDisabledSubtitle": "Disabled - Use password only", "appearanceSettings": "Appearance Settings", "changeLanguage": "Change Language", "changeLanguageSubtitle": "Choose your preferred app language", "selectLanguage": "Select Language", "biometricEnabled": "Biometric login enabled successfully", "biometricDisabled": "Biometric login disabled successfully", "biometricSetup": "Setup Biometric Login", "biometricSetupMessage": "To enable biometric login, please enter your login credentials:", "enable": "Enable", "languageChanged": "Language changed successfully", "languageChangeError": "Error occurred while changing language", "cancel": "Cancel", "enterEmail": "Enter email", "enterPassword": "Enter password", "importantNotes": "Important Notes", "importantNotesMessage": "Make sure the new password is strong and secure. It must be at least 8 characters long.", "updatepassword": "Update Password", "currentpassword": "Current Password", "currentpasswordhint": "Enter current password", "currentpassworderror": "Please enter current password", "newpassword": "New Password", "newpasswordhint": "Enter new password", "confirmpassword": "Confirm Password", "confirmpasswordhint": "Re-enter new password to confirm", "confirmpassworderror": "Please confirm new password", "passwordrequirement8": "Password must be at least 8 characters long", "passworddoesnotmatch": "Password does not match", "passworderror": "Current password is incorrect. Please check and try again.", "error": "Error", "success": "Success", "passwordupdatedsuccessfully": "Password updated successfully", "ok": "OK", "selectleavetype": "Select Leave Type", "newleaverequest": "New Leave Request", "startdate": "Start Date", "enddate": "End Date", "numberOfDays": "Number of Days", "day": "Day", "availablebalance": "Available Balance", "publicholidays": "Public Holidays", "selectdate": "Select Date", "submitleaverequest": "Submit Leave Request", "daysoftheweek1": "Monday", "daysoftheweek2": "Tuesday", "daysoftheweek3": "Wednesday", "daysoftheweek4": "Thursday", "daysoftheweek5": "Friday", "daysoftheweek6": "Saturday", "daysoftheweek7": "Sunday", "norecordsfound": "No leave requests found.", "leaverequests": "Leave Requests", "filteredrequests": "Filtered Requests", "showall": "Show All", "all": "All", "confirm": "Pending", "validate": "validated", "validate1": "Approved", "refuse": "Rejected", "from": "From", "to": "To", "show": "Show", "leaveapproval": "Leave Approval", "doyouwanttoapprove": "Do you want to approve leave request for", "leaverejection": "Leave Rejection", "doyouwanttoreject": "Do you want to reject leave request for", "reject": "Reject", "leaverejected": "Leave request rejected", "norequests": "No pending requests.", "leavesearch": "Search by employee name or leave type..", "filterbyleavetype": "Filter by leave type", "leavetype": "Leave Type", "reassign": "Reassign", "wfv": "Approved - Waiting for validation", "rhbv": "Request has been validated", "rejectrequest": "Request has been rejected"}