import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../config/app_config.dart';
import 'environment_service.dart';

/// خدمة إدارة الجلسات مع مهلة زمنية لتحسين الأمان
/// تراقب انتهاء صلاحية الجلسات وتطبق تسجيل خروج تلقائي
class SessionManagerService {
  static SessionManagerService? _instance;
  static SessionManagerService get instance {
    _instance ??= SessionManagerService._internal();
    return _instance!;
  }

  SessionManagerService._internal();

  /// مؤقت مراقبة الجلسة
  Timer? _sessionTimer;

  /// مؤقت تنبيه انتهاء الجلسة
  Timer? _warningTimer;

  /// وقت بداية الجلسة
  DateTime? _sessionStartTime;

  /// وقت آخر نشاط في الجلسة
  DateTime? _lastActivityTime;

  /// حالة الجلسة النشطة
  bool _isSessionActive = false;

  /// معرف المستخدم الحالي
  int? _currentUserId;

  /// دالة callback لتنبيه انتهاء الجلسة
  Function()? _onSessionWarning;

  /// دالة callback لانتهاء الجلسة
  Function()? _onSessionExpired;

  /// دالة callback لتجديد النشاط
  Function()? _onActivityDetected;

  // مفاتيح التخزين المحلي
  static const String _keySessionStartTime = 'session_start_time';
  static const String _keyLastActivityTime = 'last_activity_time';
  static const String _keyCurrentUserId = 'current_user_id';

  /// تهيئة خدمة إدارة الجلسات
  Future<void> initialize() async {
    try {
      // استرداد بيانات الجلسة المحفوظة
      await _restoreSessionData();
    } catch (e) {}
  }

  /// بدء جلسة جديدة
  Future<void> startSession(int userId) async {
    try {
      final now = DateTime.now();

      _currentUserId = userId;
      _sessionStartTime = now;
      _lastActivityTime = now;
      _isSessionActive = true;

      // حفظ بيانات الجلسة
      await _saveSessionData();

      // بدء مراقبة الجلسة
      _startSessionMonitoring();
    } catch (e) {}
  }

  /// تسجيل نشاط المستخدم
  Future<void> recordActivity() async {
    if (!_isSessionActive) return;

    try {
      _lastActivityTime = DateTime.now();
      await _saveSessionData();

      // إعادة تشغيل مراقبة الجلسة
      _startSessionMonitoring();

      // استدعاء callback تجديد النشاط
      _onActivityDetected?.call();
    } catch (e) {}
  }

  /// إنهاء الجلسة
  Future<void> endSession() async {
    try {
      _isSessionActive = false;
      _currentUserId = null;
      _sessionStartTime = null;
      _lastActivityTime = null;

      // إيقاف المؤقتات
      _sessionTimer?.cancel();
      _warningTimer?.cancel();

      // مسح بيانات الجلسة المحفوظة
      await _clearSessionData();
    } catch (e) {}
  }

  /// التحقق من صحة الجلسة
  bool isSessionValid() {
    if (!_isSessionActive || _lastActivityTime == null) {
      return false;
    }

    final now = DateTime.now();
    final timeSinceLastActivity = now.difference(_lastActivityTime!);
    final timeoutDuration = Duration(minutes: getSessionTimeoutMinutes());

    return timeSinceLastActivity < timeoutDuration;
  }

  /// الحصول على الوقت المتبقي للجلسة بالدقائق
  int getRemainingMinutes() {
    if (!_isSessionActive || _lastActivityTime == null) {
      return 0;
    }

    final now = DateTime.now();
    final timeSinceLastActivity = now.difference(_lastActivityTime!);
    final timeoutDuration = Duration(minutes: getSessionTimeoutMinutes());
    final remainingDuration = timeoutDuration - timeSinceLastActivity;

    return remainingDuration.inMinutes.clamp(0, getSessionTimeoutMinutes());
  }

  /// الحصول على مدة الجلسة بالدقائق من الإعدادات
  int getSessionTimeoutMinutes() {
    try {
      return EnvironmentService.getSessionTimeoutMinutes();
    } catch (e) {
      // القيمة الافتراضية في حالة عدم توفر الإعدادات
      return AppConfig.defaultSessionTimeoutMinutes;
    }
  }

  /// الحصول على مدة التنبيه قبل انتهاء الجلسة بالدقائق
  int getWarningMinutes() {
    try {
      return EnvironmentService.getSessionWarningMinutes();
    } catch (e) {
      // القيمة الافتراضية في حالة عدم توفر الإعدادات
      return AppConfig.defaultSessionWarningMinutes;
    }
  }

  /// تعيين دالة callback لتنبيه انتهاء الجلسة
  void setOnSessionWarning(Function() callback) {
    _onSessionWarning = callback;
  }

  /// تعيين دالة callback لانتهاء الجلسة
  void setOnSessionExpired(Function() callback) {
    _onSessionExpired = callback;
  }

  /// تعيين دالة callback لتجديد النشاط
  void setOnActivityDetected(Function() callback) {
    _onActivityDetected = callback;
  }

  /// بدء مراقبة الجلسة
  void _startSessionMonitoring() {
    // إيقاف المؤقتات السابقة
    _sessionTimer?.cancel();
    _warningTimer?.cancel();

    if (!_isSessionActive) return;

    final timeoutMinutes = getSessionTimeoutMinutes();
    final warningMinutes = getWarningMinutes();

    // مؤقت التنبيه
    final warningDuration = Duration(minutes: timeoutMinutes - warningMinutes);
    _warningTimer = Timer(warningDuration, () {
      if (_isSessionActive) {
        _onSessionWarning?.call();
      }
    });

    // مؤقت انتهاء الجلسة
    final timeoutDuration = Duration(minutes: timeoutMinutes);
    _sessionTimer = Timer(timeoutDuration, () {
      if (_isSessionActive) {
        _handleSessionExpired();
      }
    });
  }

  /// معالجة انتهاء الجلسة
  void _handleSessionExpired() async {
    if (kDebugMode) {
      debugPrint('⏰ انتهت مهلة الجلسة - تسجيل خروج تلقائي');
    }

    await endSession();
    _onSessionExpired?.call();
  }

  /// حفظ بيانات الجلسة
  Future<void> _saveSessionData() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      if (_sessionStartTime != null) {
        await prefs.setString(
          _keySessionStartTime,
          _sessionStartTime!.toIso8601String(),
        );
      }

      if (_lastActivityTime != null) {
        await prefs.setString(
          _keyLastActivityTime,
          _lastActivityTime!.toIso8601String(),
        );
      }

      if (_currentUserId != null) {
        await prefs.setInt(_keyCurrentUserId, _currentUserId!);
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('⚠️ خطأ في حفظ بيانات الجلسة: $e');
      }
    }
  }

  /// استرداد بيانات الجلسة
  Future<void> _restoreSessionData() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      final sessionStartStr = prefs.getString(_keySessionStartTime);
      if (sessionStartStr != null) {
        _sessionStartTime = DateTime.parse(sessionStartStr);
      }

      final lastActivityStr = prefs.getString(_keyLastActivityTime);
      if (lastActivityStr != null) {
        _lastActivityTime = DateTime.parse(lastActivityStr);
      }

      _currentUserId = prefs.getInt(_keyCurrentUserId);

      // التحقق من صحة الجلسة المسترجعة
      if (_lastActivityTime != null && isSessionValid()) {
        _isSessionActive = true;
        _startSessionMonitoring();
      } else {
        // مسح الجلسة المنتهية الصلاحية
        await _clearSessionData();
      }
    } catch (e) {}
  }

  /// مسح بيانات الجلسة المحفوظة
  Future<void> _clearSessionData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_keySessionStartTime);
      await prefs.remove(_keyLastActivityTime);
      await prefs.remove(_keyCurrentUserId);
    } catch (e) {
      if (kDebugMode) {
        debugPrint('⚠️ خطأ في مسح بيانات الجلسة: $e');
      }
    }
  }

  /// الحصول على معلومات الجلسة الحالية
  Map<String, dynamic> getSessionInfo() {
    return {
      'isActive': _isSessionActive,
      'userId': _currentUserId,
      'startTime': _sessionStartTime?.toIso8601String(),
      'lastActivity': _lastActivityTime?.toIso8601String(),
      'remainingMinutes': getRemainingMinutes(),
      'timeoutMinutes': getSessionTimeoutMinutes(),
      'warningMinutes': getWarningMinutes(),
    };
  }

  /// تنظيف الخدمة
  void dispose() {
    _sessionTimer?.cancel();
    _warningTimer?.cancel();
    _onSessionWarning = null;
    _onSessionExpired = null;
    _onActivityDetected = null;
  }
}
