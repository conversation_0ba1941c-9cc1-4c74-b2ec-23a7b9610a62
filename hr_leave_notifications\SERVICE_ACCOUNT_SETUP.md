# إعداد Service Account للـ Firebase V1 API

## 🔑 **خطوات الحصول على Service Account:**

### 1. **اذهب إلى Firebase Console:**
- https://console.firebase.google.com
- اختر مشروعك (Odoo Employee App)

### 2. **اذهب إلى Project Settings:**
- اضغط على ⚙️ → Project Settings
- تبويب **Service accounts**

### 3. **إنشاء Private Key:**
- اضغط **Generate new private key**
- سيتم تحميل ملف JSON

### 4. **نسخ محتوى JSON:**
انسخ محتوى الملف واستبدله في الكود:

```python
SERVICE_ACCOUNT_JSON = ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
```

## 🔧 **تحديث الكود:**

بعد الحصول على Service Account JSON، استبدل في `res_users.py`:

```python
def _get_access_token(self):
    """Get access token for FCM V1 API"""
    try:
        SERVICE_ACCOUNT_JSON = {
            # ضع محتوى JSON هنا
        }
        
        credentials = service_account.Credentials.from_service_account_info(
            SERVICE_ACCOUNT_JSON,
            scopes=['https://www.googleapis.com/auth/firebase.messaging']
        )
        
        request = Request()
        credentials.refresh(request)
        return credentials.token
        
    except Exception as e:
        _logger.error(f'Error getting access token: {str(e)}')
        return None
```

## 🎯 **النتيجة:**
بعد إضافة Service Account، ستعمل الإشعارات مع Firebase V1 API الجديد! 🚀

## 📝 **ملاحظة:**
حالياً الكود يستخدم الطريقة البديلة (تسجيل في logs) للاختبار حتى تضيف Service Account.
