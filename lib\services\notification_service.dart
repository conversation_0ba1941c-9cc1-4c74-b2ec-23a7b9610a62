import 'package:flutter/foundation.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'firebase_analytics_service.dart';

/// خدمة الإشعارات باستخدام Firebase Cloud Messaging
class NotificationService {
  static final FirebaseMessaging _messaging = FirebaseMessaging.instance;
  static String? _fcmToken;

  /// تهيئة خدمة الإشعارات
  static Future<void> initialize() async {
    try {
      // طلب الصلاحيات
      NotificationSettings settings = await _messaging.requestPermission(
        alert: true,
        badge: true,
        sound: true,
        provisional: false,
      );

      if (settings.authorizationStatus == AuthorizationStatus.authorized) {
        debugPrint('✅ تم منح صلاحيات الإشعارات');

        // الحصول على FCM Token
        _fcmToken = await _messaging.getToken();
        debugPrint('📱 FCM Token: $_fcmToken');

        // إعداد معالجات الإشعارات
        _setupMessageHandlers();

        // تتبع تفعيل الإشعارات
        await FirebaseAnalyticsService.logEvent(
          name: 'notifications_enabled',
          parameters: {'status': 'authorized'},
        );
      } else {
        debugPrint('❌ تم رفض صلاحيات الإشعارات');
        await FirebaseAnalyticsService.logEvent(
          name: 'notifications_disabled',
          parameters: {'status': settings.authorizationStatus.toString()},
        );
      }
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة الإشعارات: $e');
    }
  }

  /// إعداد معالجات الإشعارات
  static void _setupMessageHandlers() {
    // الإشعارات عندما التطبيق مفتوح
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      debugPrint('📨 إشعار جديد: ${message.notification?.title}');
      _handleMessage(message);
    });

    // الإشعارات عندما التطبيق في الخلفية
    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      debugPrint('📱 فتح التطبيق من الإشعار: ${message.notification?.title}');
      _handleMessage(message);
    });
  }

  /// معالجة الإشعار الواصل
  static void _handleMessage(RemoteMessage message) {
    final data = message.data;
    final type = data['type'];

    switch (type) {
      case 'leave_approved':
        _handleLeaveApproved(data);
        break;
      case 'leave_rejected':
        _handleLeaveRejected(data);
        break;
      case 'new_leave_request':
        _handleNewLeaveRequest(data);
        break;
      default:
        debugPrint('نوع إشعار غير معروف: $type');
    }
  }

  /// معالجة إشعار قبول الإجازة
  static void _handleLeaveApproved(Map<String, dynamic> data) {
    FirebaseAnalyticsService.logEvent(
      name: 'notification_received',
      parameters: {
        'type': 'leave_approved',
        'leave_id': data['leave_id'] ?? '',
      },
    );
  }

  /// معالجة إشعار رفض الإجازة
  static void _handleLeaveRejected(Map<String, dynamic> data) {
    FirebaseAnalyticsService.logEvent(
      name: 'notification_received',
      parameters: {
        'type': 'leave_rejected',
        'leave_id': data['leave_id'] ?? '',
        'reason': data['reason'] ?? '',
      },
    );
  }

  /// معالجة إشعار طلب إجازة جديد
  static void _handleNewLeaveRequest(Map<String, dynamic> data) {
    FirebaseAnalyticsService.logEvent(
      name: 'notification_received',
      parameters: {
        'type': 'new_leave_request',
        'leave_id': data['leave_id'] ?? '',
        'employee_name': data['employee_name'] ?? '',
      },
    );
  }

  /// الحصول على FCM Token
  static String? get fcmToken => _fcmToken;

  /// تحديث FCM Token
  static Future<void> refreshToken() async {
    try {
      _fcmToken = await _messaging.getToken();
      debugPrint('🔄 تم تحديث FCM Token: $_fcmToken');
    } catch (e) {
      debugPrint('❌ خطأ في تحديث FCM Token: $e');
    }
  }

  /// إرسال FCM Token للخادم
  static Future<bool> sendTokenToServer({
    required String serverUrl,
    required int userId,
    required String password,
    required String database,
  }) async {
    if (_fcmToken == null) {
      debugPrint('❌ لا يوجد FCM Token للإرسال');
      return false;
    }

    try {
      debugPrint(
        '📤 إرسال FCM Token للخادم: ${_fcmToken!.substring(0, 20)}...',
      );

      // إعداد البيانات للإرسال
      final requestData = {
        "jsonrpc": "2.0",
        "method": "call",
        "params": {
          "service": "object",
          "method": "execute_kw",
          "args": [
            database,
            userId,
            password,
            "res.users",
            "save_fcm_token",
            [userId, _fcmToken, defaultTargetPlatform.name.toLowerCase()],
          ],
        },
        "id": DateTime.now().millisecondsSinceEpoch,
      };

      debugPrint(
        '� بيانات الطلب: ${requestData.toString().substring(0, 100)}...',
      );

      // إرسال الطلب إلى خادم Odoo
      final response = await http
          .post(
            Uri.parse('$serverUrl/web/dataset/call_kw'),
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
            },
            body: jsonEncode(requestData),
          )
          .timeout(
            const Duration(seconds: 10),
            onTimeout: () {
              throw Exception('انتهت مهلة الاتصال مع الخادم');
            },
          );

      debugPrint('📡 رد الخادم - كود الحالة: ${response.statusCode}');

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        debugPrint('📡 رد الخادم: $responseData');

        if (responseData['error'] != null) {
          debugPrint('❌ خطأ من الخادم: ${responseData['error']}');
          return false;
        }

        final result = responseData['result'];
        if (result is Map && result['success'] == true) {
          debugPrint('✅ تم حفظ FCM Token بنجاح');

          // تتبع نجاح إرسال Token
          await FirebaseAnalyticsService.logEvent(
            name: 'fcm_token_sent',
            parameters: {
              'success': 'true',
              'platform': defaultTargetPlatform.name.toLowerCase(),
            },
          );

          return true;
        } else {
          debugPrint('❌ فشل في حفظ FCM Token: $result');
          return false;
        }
      } else {
        debugPrint('❌ خطأ HTTP: ${response.statusCode} - ${response.body}');
        return false;
      }
    } catch (e) {
      debugPrint('❌ خطأ في إرسال Token للخادم: $e');

      // تتبع فشل إرسال Token
      await FirebaseAnalyticsService.logEvent(
        name: 'fcm_token_send_failed',
        parameters: {
          'error': e.toString(),
          'platform': defaultTargetPlatform.name.toLowerCase(),
        },
      );

      return false;
    }
  }

  /// إرسال إشعار تجريبي للاختبار
  static Future<bool> sendTestNotification({
    required String serverUrl,
    required int userId,
    required String password,
    required String database,
  }) async {
    try {
      debugPrint('🧪 إرسال إشعار تجريبي...');

      // إعداد البيانات للإرسال
      final requestData = {
        "jsonrpc": "2.0",
        "method": "call",
        "params": {
          "service": "object",
          "method": "execute_kw",
          "args": [
            database,
            userId,
            password,
            "res.users",
            "test_fcm_notification",
            [userId],
          ],
        },
        "id": DateTime.now().millisecondsSinceEpoch,
      };

      // إرسال الطلب إلى خادم Odoo
      final response = await http
          .post(
            Uri.parse('$serverUrl/web/dataset/call_kw'),
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
            },
            body: jsonEncode(requestData),
          )
          .timeout(
            const Duration(seconds: 15),
            onTimeout: () {
              throw Exception('انتهت مهلة الاتصال مع الخادم');
            },
          );

      debugPrint('📡 رد الخادم - كود الحالة: ${response.statusCode}');

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        debugPrint('📡 رد الخادم: $responseData');

        if (responseData['error'] != null) {
          debugPrint('❌ خطأ من الخادم: ${responseData['error']}');
          return false;
        }

        final result = responseData['result'];
        if (result is Map && result['success'] == true) {
          debugPrint('✅ تم إرسال الإشعار التجريبي بنجاح');

          // تتبع نجاح إرسال الإشعار التجريبي
          await FirebaseAnalyticsService.logEvent(
            name: 'test_notification_sent',
            parameters: {
              'success': 'true',
              'platform': defaultTargetPlatform.name.toLowerCase(),
            },
          );

          return true;
        } else {
          debugPrint('❌ فشل في إرسال الإشعار التجريبي: $result');
          return false;
        }
      } else {
        debugPrint('❌ خطأ HTTP: ${response.statusCode} - ${response.body}');
        return false;
      }
    } catch (e) {
      debugPrint('❌ خطأ في إرسال الإشعار التجريبي: $e');

      // تتبع فشل إرسال الإشعار التجريبي
      await FirebaseAnalyticsService.logEvent(
        name: 'test_notification_failed',
        parameters: {
          'error': e.toString(),
          'platform': defaultTargetPlatform.name.toLowerCase(),
        },
      );

      return false;
    }
  }

  /// إلغاء الاشتراك في الإشعارات
  static Future<void> unsubscribe() async {
    try {
      await _messaging.deleteToken();
      _fcmToken = null;
      debugPrint('🚫 تم إلغاء الاشتراك في الإشعارات');
    } catch (e) {
      debugPrint('❌ خطأ في إلغاء الاشتراك: $e');
    }
  }
}
