import 'package:flutter/foundation.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'firebase_analytics_service.dart';

/// خدمة الإشعارات باستخدام Firebase Cloud Messaging
class NotificationService {
  static final FirebaseMessaging _messaging = FirebaseMessaging.instance;
  static String? _fcmToken;

  /// تهيئة خدمة الإشعارات
  static Future<void> initialize() async {
    try {
      // طلب الصلاحيات
      NotificationSettings settings = await _messaging.requestPermission(
        alert: true,
        badge: true,
        sound: true,
        provisional: false,
      );

      if (settings.authorizationStatus == AuthorizationStatus.authorized) {
        debugPrint('✅ تم منح صلاحيات الإشعارات');
        
        // الحصول على FCM Token
        _fcmToken = await _messaging.getToken();
        debugPrint('📱 FCM Token: $_fcmToken');

        // إعداد معالجات الإشعارات
        _setupMessageHandlers();
        
        // تتبع تفعيل الإشعارات
        await FirebaseAnalyticsService.logEvent(
          name: 'notifications_enabled',
          parameters: {'status': 'authorized'},
        );
      } else {
        debugPrint('❌ تم رفض صلاحيات الإشعارات');
        await FirebaseAnalyticsService.logEvent(
          name: 'notifications_disabled',
          parameters: {'status': settings.authorizationStatus.toString()},
        );
      }
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة الإشعارات: $e');
    }
  }

  /// إعداد معالجات الإشعارات
  static void _setupMessageHandlers() {
    // الإشعارات عندما التطبيق مفتوح
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      debugPrint('📨 إشعار جديد: ${message.notification?.title}');
      _handleMessage(message);
    });

    // الإشعارات عندما التطبيق في الخلفية
    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      debugPrint('📱 فتح التطبيق من الإشعار: ${message.notification?.title}');
      _handleMessage(message);
    });
  }

  /// معالجة الإشعار الواصل
  static void _handleMessage(RemoteMessage message) {
    final data = message.data;
    final type = data['type'];

    switch (type) {
      case 'leave_approved':
        _handleLeaveApproved(data);
        break;
      case 'leave_rejected':
        _handleLeaveRejected(data);
        break;
      case 'new_leave_request':
        _handleNewLeaveRequest(data);
        break;
      default:
        debugPrint('نوع إشعار غير معروف: $type');
    }
  }

  /// معالجة إشعار قبول الإجازة
  static void _handleLeaveApproved(Map<String, dynamic> data) {
    FirebaseAnalyticsService.logEvent(
      name: 'notification_received',
      parameters: {
        'type': 'leave_approved',
        'leave_id': data['leave_id'] ?? '',
      },
    );
  }

  /// معالجة إشعار رفض الإجازة
  static void _handleLeaveRejected(Map<String, dynamic> data) {
    FirebaseAnalyticsService.logEvent(
      name: 'notification_received',
      parameters: {
        'type': 'leave_rejected',
        'leave_id': data['leave_id'] ?? '',
        'reason': data['reason'] ?? '',
      },
    );
  }

  /// معالجة إشعار طلب إجازة جديد
  static void _handleNewLeaveRequest(Map<String, dynamic> data) {
    FirebaseAnalyticsService.logEvent(
      name: 'notification_received',
      parameters: {
        'type': 'new_leave_request',
        'leave_id': data['leave_id'] ?? '',
        'employee_name': data['employee_name'] ?? '',
      },
    );
  }

  /// الحصول على FCM Token
  static String? get fcmToken => _fcmToken;

  /// تحديث FCM Token
  static Future<void> refreshToken() async {
    try {
      _fcmToken = await _messaging.getToken();
      debugPrint('🔄 تم تحديث FCM Token: $_fcmToken');
    } catch (e) {
      debugPrint('❌ خطأ في تحديث FCM Token: $e');
    }
  }

  /// إرسال FCM Token للخادم
  static Future<bool> sendTokenToServer({
    required String serverUrl,
    required int userId,
    required String password,
  }) async {
    if (_fcmToken == null) return false;

    try {
      // هنا يتم إرسال Token لخادم Odoo
      // يجب تطوير API endpoint في Odoo لحفظ Token
      debugPrint('📤 إرسال FCM Token للخادم: $_fcmToken');
      
      // مثال على البيانات المرسلة:
      final tokenData = {
        'user_id': userId,
        'fcm_token': _fcmToken,
        'platform': defaultTargetPlatform.name,
        'app_version': '1.0.0',
      };
      
      debugPrint('📊 بيانات Token: $tokenData');
      
      // TODO: إرسال فعلي للخادم
      return true;
    } catch (e) {
      debugPrint('❌ خطأ في إرسال Token للخادم: $e');
      return false;
    }
  }

  /// إلغاء الاشتراك في الإشعارات
  static Future<void> unsubscribe() async {
    try {
      await _messaging.deleteToken();
      _fcmToken = null;
      debugPrint('🚫 تم إلغاء الاشتراك في الإشعارات');
    } catch (e) {
      debugPrint('❌ خطأ في إلغاء الاشتراك: $e');
    }
  }
}
