// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appName => 'Employee Bank';

  @override
  String get appSubtitle => 'Banking Employee Management System';

  @override
  String get login => 'Login';

  @override
  String get rememberMe => 'Remember Me';

  @override
  String get uncheckrememberme =>
      'Uncheck \'Remember Me\' because biometric is enabled';

  @override
  String get validemail => 'Please enter a valid email';

  @override
  String get correctpassword => 'Password must be at least 6 characters';

  @override
  String get securityInformation => 'Security Information';

  @override
  String get rememberMeInfo =>
      'When you enable this option, your login credentials will be securely stored on your device to make future logins easier.\n\nYou can disable this option at any time from the app settings.';

  @override
  String get understood => 'Understood';

  @override
  String get identification => 'Identification';

  @override
  String get directmanager => 'Direct Manager';

  @override
  String get quickactions => 'Quick Actions';

  @override
  String get myprofile => 'My Profile';

  @override
  String get leaveTypes => 'Leave Types';

  @override
  String get myLeaveRequests => 'My Requests';

  @override
  String get leaveApprovals => 'Approvals';

  @override
  String get retry => 'Retry';

  @override
  String get settings => 'Settings';

  @override
  String get appSettings => 'App Settings';

  @override
  String get customizeSettings =>
      'Customize app settings according to your needs';

  @override
  String get accountSettings => 'Account Settings';

  @override
  String get changePassword => 'Change Password';

  @override
  String get changePasswordSubtitle => 'Update your password';

  @override
  String get securitySettings => 'Security Settings';

  @override
  String get biometricLogin => 'Biometric Login';

  @override
  String get biometricEnabledSubtitle =>
      'Enabled - You can login with biometric';

  @override
  String get biometricDisabledSubtitle => 'Disabled - Use password only';

  @override
  String get appearanceSettings => 'Appearance Settings';

  @override
  String get changeLanguage => 'Change Language';

  @override
  String get changeLanguageSubtitle => 'Choose your preferred app language';

  @override
  String get selectLanguage => 'Select Language';

  @override
  String get biometricEnabled => 'Biometric login enabled successfully';

  @override
  String get biometricDisabled => 'Biometric login disabled successfully';

  @override
  String get biometricSetup => 'Setup Biometric Login';

  @override
  String get biometricSetupMessage =>
      'To enable biometric login, please enter your login credentials:';

  @override
  String get enable => 'Enable';

  @override
  String get languageChanged => 'Language changed successfully';

  @override
  String get languageChangeError => 'Error occurred while changing language';

  @override
  String get cancel => 'Cancel';

  @override
  String get enterEmail => 'Enter email';

  @override
  String get enterPassword => 'Enter password';

  @override
  String get importantNotes => 'Important Notes';

  @override
  String get importantNotesMessage =>
      'Make sure the new password is strong and secure. It must be at least 8 characters long.';

  @override
  String get updatepassword => 'Update Password';

  @override
  String get currentpassword => 'Current Password';

  @override
  String get currentpasswordhint => 'Enter current password';

  @override
  String get currentpassworderror => 'Please enter current password';

  @override
  String get newpassword => 'New Password';

  @override
  String get newpasswordhint => 'Enter new password';

  @override
  String get confirmpassword => 'Confirm Password';

  @override
  String get confirmpasswordhint => 'Re-enter new password to confirm';

  @override
  String get confirmpassworderror => 'Please confirm new password';

  @override
  String get passwordrequirement8 =>
      'Password must be at least 8 characters long';

  @override
  String get passworddoesnotmatch => 'Password does not match';

  @override
  String get passworderror =>
      'Current password is incorrect. Please check and try again.';

  @override
  String get error => 'Error';

  @override
  String get success => 'Success';

  @override
  String get passwordupdatedsuccessfully => 'Password updated successfully';

  @override
  String get ok => 'OK';

  @override
  String get selectleavetype => 'Select Leave Type';

  @override
  String get newleaverequest => 'New Leave Request';

  @override
  String get startdate => 'Start Date';

  @override
  String get enddate => 'End Date';

  @override
  String get numberOfDays => 'Number of Days';

  @override
  String get day => 'Day';

  @override
  String get availablebalance => 'Available Balance';

  @override
  String get publicholidays => 'Public Holidays';

  @override
  String get selectdate => 'Select Date';

  @override
  String get submitleaverequest => 'Submit Leave Request';

  @override
  String get daysoftheweek1 => 'Monday';

  @override
  String get daysoftheweek2 => 'Tuesday';

  @override
  String get daysoftheweek3 => 'Wednesday';

  @override
  String get daysoftheweek4 => 'Thursday';

  @override
  String get daysoftheweek5 => 'Friday';

  @override
  String get daysoftheweek6 => 'Saturday';

  @override
  String get daysoftheweek7 => 'Sunday';

  @override
  String get norecordsfound => 'No leave requests found.';

  @override
  String get leaverequests => 'Leave Requests';

  @override
  String get filteredrequests => 'Filtered Requests';

  @override
  String get showall => 'Show All';

  @override
  String get all => 'All';

  @override
  String get confirm => 'Pending';

  @override
  String get validate => 'validated';

  @override
  String get validate1 => 'Approved';

  @override
  String get refuse => 'Rejected';

  @override
  String get from => 'From';

  @override
  String get to => 'To';

  @override
  String get show => 'Show';

  @override
  String get leaveapproval => 'Leave Approval';

  @override
  String get doyouwanttoapprove => 'Do you want to approve leave request for';

  @override
  String get leaverejection => 'Leave Rejection';

  @override
  String get doyouwanttoreject => 'Do you want to reject leave request for';

  @override
  String get reject => 'Reject';

  @override
  String get leaverejected => 'Leave request rejected';

  @override
  String get norequests => 'No pending requests.';

  @override
  String get leavesearch => 'Search by employee name or leave type..';

  @override
  String get filterbyleavetype => 'Filter by leave type';

  @override
  String get leavetype => 'Leave Type';

  @override
  String get reassign => 'Reassign';

  @override
  String get wfv => 'Approved - Waiting for validation';

  @override
  String get rhbv => 'Request has been validated';

  @override
  String get rejectrequest => 'Request has been rejected';
}
