import 'package:flutter/material.dart';
import '../generated/l10n/app_localizations.dart';

/// نموذج بيانات طلب الإجازة
class LeaveRequest {
  final int? id;
  final int holidayStatusId;
  final String holidayStatusName;
  final DateTime dateFrom;
  final DateTime dateTo;
  final double numberOfDays;
  final String state;

  LeaveRequest({
    this.id,
    required this.holidayStatusId,
    required this.holidayStatusName,
    required this.dateFrom,
    required this.dateTo,
    required this.numberOfDays,
    required this.state,
  });

  // خريطة تحويل حالات الطلب إلى الألوان المقابلة
  static const Map<String, int> _stateColorMap = {
    'draft': 0xFF9E9E9E, // رمادي
    'confirm': 0xFFFF9800, // برتقالي
    'validate1': 0xFF2196F3, // أزرق - Second Approval
    'validate': 0xFF4CAF50, // أخضر
    'refuse': 0xFFF44336, // أحمر
  };

  /// الحصول على نص حالة الطلب المترجم
  String getStateText(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    switch (state) {
      case 'draft':
        return 'مسودة'; // لا توجد ترجمة لهذه الحالة في ملفات الترجمة
      case 'confirm':
        return localizations.confirm;
      case 'validate1':
        return localizations.validate1;
      case 'validate':
        return localizations.validate;
      case 'refuse':
        return localizations.refuse;
      default:
        return state;
    }
  }

  /// الحصول على نص حالة الطلب (للتوافق مع الكود القديم)
  @Deprecated('استخدم getStateText(context) بدلاً من ذلك')
  String get stateText {
    // نصوص افتراضية للتوافق مع الكود القديم
    const stateMap = {
      'draft': 'مسودة',
      'confirm': 'قيد الانتظار',
      'validate1': 'تم الموافقة',
      'validate': 'تم الاعتماد',
      'refuse': 'مرفوض',
    };
    return stateMap[state] ?? state;
  }

  /// الحصول على لون حالة الطلب
  int get stateColor {
    return _stateColorMap[state] ?? 0xFF9E9E9E;
  }

  /// إنشاء كائن LeaveRequest من البيانات المستلمة من Odoo
  factory LeaveRequest.fromOdooData(Map<String, dynamic> data) {
    // استخراج اسم نوع الإجازة من holiday_status_id إذا كان موجود
    String holidayStatusName = 'غير محدد';
    int holidayStatusId = 0;

    if (data['holiday_status_id'] != null) {
      if (data['holiday_status_id'] is List) {
        final statusData = data['holiday_status_id'] as List;
        if (statusData.length >= 2) {
          holidayStatusId = statusData[0] as int;
          holidayStatusName = statusData[1] as String;
        }
      } else if (data['holiday_status_id'] is int) {
        holidayStatusId = data['holiday_status_id'] as int;
        holidayStatusName = 'نوع إجازة $holidayStatusId';
      }
    }

    // معالجة التواريخ بحذر
    DateTime dateFrom = DateTime.now();
    DateTime dateTo = DateTime.now();

    try {
      if (data['request_date_from'] != null) {
        dateFrom = DateTime.parse(data['request_date_from'] as String);
      } else if (data['date_from'] != null) {
        dateFrom = DateTime.parse(data['date_from'] as String);
      }
    } catch (e) {
      debugPrint('خطأ في تحليل تاريخ البداية: $e');
    }

    try {
      if (data['request_date_to'] != null) {
        dateTo = DateTime.parse(data['request_date_to'] as String);
      } else if (data['date_to'] != null) {
        dateTo = DateTime.parse(data['date_to'] as String);
      }
    } catch (e) {
      debugPrint('خطأ في تحليل تاريخ النهاية: $e');
    }

    // معالجة عدد الأيام
    double numberOfDays = 1.0;
    if (data['number_of_days'] != null) {
      try {
        numberOfDays = (data['number_of_days'] as num).toDouble();
      } catch (e) {
        debugPrint('خطأ في تحليل عدد الأيام: $e');
      }
    }

    // معالجة الحالة
    String state = 'draft';
    if (data['state'] != null && data['state'] is String) {
      state = data['state'] as String;
    }

    return LeaveRequest(
      id: data['id'] as int?,
      holidayStatusId: holidayStatusId,
      holidayStatusName: holidayStatusName,
      dateFrom: dateFrom,
      dateTo: dateTo,
      numberOfDays: numberOfDays,
      state: state,
    );
  }

  /// تحويل الكائن إلى Map لإرساله إلى Odoo
  Map<String, dynamic> toOdooData() {
    return {
      'holiday_status_id': holidayStatusId,
      'date_from': dateFrom.toIso8601String(),
      'date_to': dateTo.toIso8601String(),
      'number_of_days': numberOfDays,
      'state': state,
    };
  }

  /// تحويل الكائن إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'holidayStatusId': holidayStatusId,
      'holidayStatusName': holidayStatusName,
      'dateFrom': dateFrom.toIso8601String(),
      'dateTo': dateTo.toIso8601String(),
      'numberOfDays': numberOfDays,
      'state': state,
    };
  }

  @override
  String toString() {
    return 'LeaveRequest{id: $id, holidayStatusId: $holidayStatusId, holidayStatusName: $holidayStatusName, dateFrom: $dateFrom, dateTo: $dateTo, numberOfDays: $numberOfDays, state: $state}';
  }
}
