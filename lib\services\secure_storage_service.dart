import 'dart:convert';
import 'dart:typed_data';
import 'package:encrypt/encrypt.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:crypto/crypto.dart';

/// خدمة التشفير الآمنة لحماية البيانات الحساسة
/// تستخدم تشفير AES-256 مع مفتاح مشتق من معرف الجهاز
class SecureStorageService {
  static const String _keyEncryptionKey = 'encryption_key';
  static const String _keyDeviceId = 'device_id';

  static Encrypter? _encrypter;
  static IV? _iv;

  /// تهيئة خدمة التشفير
  /// يتم إنشاء مفتاح تشفير فريد لكل جهاز
  static Future<void> initialize() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // الحصول على مفتاح التشفير أو إنشاء واحد جديد
      String? encryptionKey = prefs.getString(_keyEncryptionKey);
      if (encryptionKey == null) {
        // إنشاء مفتاح تشفير جديد
        encryptionKey = _generateSecureKey();
        await prefs.setString(_keyEncryptionKey, encryptionKey);
      }

      // إنشاء كائن التشفير
      final key = Key.fromBase64(encryptionKey);
      _encrypter = Encrypter(AES(key));

      // إنشاء IV ثابت للجهاز (في التطبيقات الإنتاجية، يُفضل IV عشوائي لكل عملية)
      String? deviceId = prefs.getString(_keyDeviceId);
      if (deviceId == null) {
        deviceId = _generateDeviceId();
        await prefs.setString(_keyDeviceId, deviceId);
      }

      // إنشاء IV من معرف الجهاز
      final ivBytes = sha256
          .convert(utf8.encode(deviceId))
          .bytes
          .take(16)
          .toList();
      _iv = IV(Uint8List.fromList(ivBytes));
    } catch (e) {
      throw Exception('فشل في تهيئة خدمة التشفير: $e');
    }
  }

  /// توليد مفتاح تشفير آمن
  static String _generateSecureKey() {
    final key = Key.fromSecureRandom(32); // AES-256
    return key.base64;
  }

  /// توليد معرف فريد للجهاز
  static String _generateDeviceId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = Key.fromSecureRandom(16).base64;
    return '$timestamp-$random';
  }

  /// تشفير نص
  /// [plainText] النص المراد تشفيره
  /// يرجع النص المشفر بصيغة Base64
  static Future<String> encrypt(String plainText) async {
    await _ensureInitialized();

    try {
      // معالجة النصوص الفارغة
      if (plainText.isEmpty) {
        plainText = ' '; // استبدال النص الفارغ بمسافة
      }

      final encrypted = _encrypter!.encrypt(plainText, iv: _iv!);
      return encrypted.base64;
    } catch (e) {
      throw Exception('فشل في تشفير البيانات: $e');
    }
  }

  /// فك تشفير نص
  /// [encryptedText] النص المشفر بصيغة Base64
  /// يرجع النص الأصلي
  static Future<String> decrypt(String encryptedText) async {
    await _ensureInitialized();

    try {
      final encrypted = Encrypted.fromBase64(encryptedText);
      final decrypted = _encrypter!.decrypt(encrypted, iv: _iv!);

      // معالجة النصوص التي كانت فارغة أصلاً
      if (decrypted == ' ') {
        return ''; // إرجاع النص الفارغ الأصلي
      }

      return decrypted;
    } catch (e) {
      throw Exception('فشل في فك تشفير البيانات: $e');
    }
  }

  /// التأكد من تهيئة الخدمة
  static Future<void> _ensureInitialized() async {
    if (_encrypter == null || _iv == null) {
      await initialize();
    }
  }

  /// تشفير وحفظ نص في SharedPreferences
  /// [key] مفتاح التخزين
  /// [value] القيمة المراد تشفيرها وحفظها
  static Future<void> setSecureString(String key, String value) async {
    try {
      final encryptedValue = await encrypt(value);
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(key, encryptedValue);
    } catch (e) {
      throw Exception('فشل في حفظ البيانات المشفرة: $e');
    }
  }

  /// استرجاع وفك تشفير نص من SharedPreferences
  /// [key] مفتاح التخزين
  /// يرجع النص الأصلي أو null إذا لم يوجد
  static Future<String?> getSecureString(String key) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final encryptedValue = prefs.getString(key);

      if (encryptedValue == null) {
        return null;
      }

      return await decrypt(encryptedValue);
    } catch (e) {
      // في حالة فشل فك التشفير، قد تكون البيانات تالفة أو من إصدار قديم
      // نحذف البيانات التالفة ونرجع null
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(key);
      return null;
    }
  }

  /// حذف بيانات مشفرة من SharedPreferences
  /// [key] مفتاح التخزين
  static Future<void> removeSecureString(String key) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(key);
  }

  /// التحقق من وجود بيانات مشفرة
  /// [key] مفتاح التخزين
  /// يرجع true إذا كانت البيانات موجودة ويمكن فك تشفيرها
  static Future<bool> hasSecureString(String key) async {
    try {
      final value = await getSecureString(key);
      return value != null;
    } catch (e) {
      return false;
    }
  }

  /// مسح جميع مفاتيح التشفير (إعادة تعيين كاملة)
  /// تستخدم عند تسجيل الخروج النهائي أو إعادة تعيين التطبيق
  static Future<void> clearAllEncryptionKeys() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_keyEncryptionKey);
    await prefs.remove(_keyDeviceId);

    // إعادة تعيين المتغيرات
    _encrypter = null;
    _iv = null;
  }
}
