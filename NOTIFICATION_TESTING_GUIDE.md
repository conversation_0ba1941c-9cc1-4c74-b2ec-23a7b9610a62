# 🧪 دليل اختبار الإشعارات السريع

## ✅ **الإصلاحات المطبقة:**

### **1. في التطبيق (Flutter):**
- ✅ إصلاح دالة `sendTokenToServer` لإرسال FCM Token فعلياً
- ✅ إضافة دالة `sendTestNotification` لاختبار الإشعارات
- ✅ إضافة زر "اختبار الإشعارات" في شاشة الإعدادات
- ✅ تحسين معالجة الأخطاء والتتبع

### **2. في Odoo:**
- ✅ تحسين دالة `save_fcm_token` مع معالجة أخطاء متقدمة
- ✅ إضافة دالة `test_fcm_notification` لإرسال إشعار تجريبي
- ✅ تحسين Firebase Admin SDK مع دعم متغيرات البيئة
- ✅ معالجة FCM tokens غير الصالحة تلقائياً

---

## 🚀 **كيفية اختبار النظام الآن:**

### **الطريقة 1: اختبار من التطبيق (الأسهل)**

1. **تشغيل التطبيق:**
   ```bash
   flutter run
   ```

2. **تسجيل الدخول:**
   - أدخل بيانات تسجيل الدخول
   - سيتم إرسال FCM Token تلقائياً للخادم

3. **اختبار الإشعارات:**
   - اذهب إلى الإعدادات (أيقونة الترس)
   - ابحث عن قسم "إعدادات الإشعارات"
   - اضغط على زر "اختبار"
   - انتظر رسالة النجاح/الفشل

### **الطريقة 2: اختبار من Odoo Shell**

```bash
# دخول إلى Odoo shell
sudo -u odoo /opt/odoo/odoo-bin shell -d YOUR_DATABASE_NAME

# في Python shell:
# البحث عن مستخدم
user = env['res.users'].search([('login', '=', '<EMAIL>')], limit=1)

# التحقق من وجود FCM Token
print(f"FCM Token: {user.fcm_token}")

# إرسال إشعار تجريبي
result = user.test_fcm_notification(user.id)
print(f"Result: {result}")
```

### **الطريقة 3: اختبار إشعارات الإجازات**

1. **إنشاء طلب إجازة:**
   - في التطبيق، اذهب إلى تبويب "طلب إجازة"
   - أنشئ طلب إجازة جديد

2. **الموافقة/الرفض من Odoo:**
   - في واجهة Odoo، اذهب إلى Leaves
   - وافق أو ارفض الطلب
   - يجب أن يصل إشعار للتطبيق

---

## 📱 **ما يجب أن تراه:**

### **عند نجاح الاختبار:**
- ✅ رسالة "تم إرسال الإشعار التجريبي بنجاح!"
- 📱 إشعار يظهر في الجهاز: "اختبار الإشعارات - مرحباً [اسم المستخدم]!"
- 📊 تسجيل في Firebase Analytics

### **في حالة الفشل:**
- ❌ رسالة خطأ واضحة تشرح المشكلة
- 📝 تسجيل مفصل في logs

---

## 🔍 **مراقبة النظام:**

### **في التطبيق (Debug Console):**
```
📤 إرسال FCM Token للخادم: [token]...
✅ تم حفظ FCM Token بنجاح
🧪 إرسال إشعار تجريبي...
✅ تم إرسال الإشعار التجريبي بنجاح
```

### **في Odoo Logs:**
```bash
# مراقبة logs الإشعارات
tail -f /var/log/odoo/odoo.log | grep -E "(FCM|notification)"

# أمثلة على الرسائل المتوقعة:
INFO: FCM Token saved for user [name]: [token]...
INFO: Test notification sent successfully to user [name]
INFO: Firebase Admin FCM notification sent successfully to [name]: [response]
```

---

## 🛠️ **استكشاف الأخطاء الشائعة:**

### **خطأ: "معلومات المستخدم غير متوفرة"**
**السبب:** لم يتم تمرير بيانات المستخدم لشاشة الإعدادات
**الحل:** تأكد من تسجيل الدخول بشكل صحيح

### **خطأ: "No FCM token for user"**
**السبب:** لم يتم حفظ FCM Token في قاعدة البيانات
**الحل:** 
1. تحقق من صلاحيات الإشعارات في الجهاز
2. أعد تسجيل الدخول
3. تحقق من اتصال الإنترنت

### **خطأ: "Firebase Admin SDK not available"**
**السبب:** لم يتم تثبيت firebase-admin
**الحل:**
```bash
pip install firebase-admin
sudo systemctl restart odoo
```

### **خطأ: "Invalid FCM token"**
**السبب:** Token منتهي الصلاحية أو غير صحيح
**الحل:** سيتم مسح Token تلقائياً، أعد تسجيل الدخول

---

## 📈 **مؤشرات النجاح:**

### **✅ النظام يعمل بشكل صحيح إذا:**
1. تم حفظ FCM Token عند تسجيل الدخول
2. زر "اختبار الإشعارات" يظهر في الإعدادات
3. الإشعار التجريبي يصل للجهاز
4. إشعارات الإجازات تعمل تلقائياً

### **📊 Firebase Analytics Events:**
- `fcm_token_sent` - عند حفظ Token
- `test_notification_sent` - عند نجاح الاختبار
- `notification_received` - عند استقبال إشعار

---

## 🎯 **الخطوات التالية:**

1. **اختبر النظام الحالي** ✋ **← ابدأ من هنا**
2. **إذا نجح الاختبار:** انتقل لإعداد Firebase الحقيقي
3. **إذا فشل الاختبار:** راجع استكشاف الأخطاء أعلاه

**ملاحظة:** النظام الحالي يستخدم Firebase تجريبي ولكن الكود محسّن ويجب أن يعمل للاختبار الأولي.
