from odoo import models, fields, api
import logging

_logger = logging.getLogger(__name__)

class HrLeave(models.Model):
    _inherit = 'hr.leave'

    @api.model
    def create(self, vals):
        """Override create to send notification to manager when new leave request is created"""
        leave = super(Hr<PERSON>eave, self).create(vals)
        
        # Send notification to manager about new leave request
        if leave.employee_id and leave.employee_id.parent_id and leave.employee_id.parent_id.user_id:
            manager_user = leave.employee_id.parent_id.user_id
            
            title = "طلب إجازة جديد"
            body = f"طلب إجازة جديد من {leave.employee_id.name} من {leave.request_date_from} إلى {leave.request_date_to}"
            data = {
                'type': 'new_leave_request',
                'leave_id': str(leave.id),
                'employee_name': leave.employee_id.name,
                'employee_id': str(leave.employee_id.id),
                'date_from': str(leave.request_date_from),
                'date_to': str(leave.request_date_to),
                'leave_type': leave.holiday_status_id.name
            }
            
            manager_user.send_fcm_notification(title, body, data)
            _logger.info(f'New leave request notification sent to manager {manager_user.name}')
        
        return leave

    def action_approve(self):
        """Override approve to send notification to employee"""
        result = super(HrLeave, self).action_approve()
        
        # Send notification to employee about approval
        if self.employee_id and self.employee_id.user_id:
            employee_user = self.employee_id.user_id
            
            title = "تم قبول طلب الإجازة"
            body = f"تم قبول طلبك للإجازة من {self.request_date_from} إلى {self.request_date_to}"
            data = {
                'type': 'leave_approved',
                'leave_id': str(self.id),
                'date_from': str(self.request_date_from),
                'date_to': str(self.request_date_to),
                'leave_type': self.holiday_status_id.name,
                'number_of_days': str(self.number_of_days)
            }
            
            employee_user.send_fcm_notification(title, body, data)
            _logger.info(f'Leave approval notification sent to employee {employee_user.name}')
        
        return result

    def action_refuse(self):
        """Override refuse to send notification to employee"""
        result = super(HrLeave, self).action_refuse()
        
        # Send notification to employee about rejection
        if self.employee_id and self.employee_id.user_id:
            employee_user = self.employee_id.user_id
            
            title = "تم رفض طلب الإجازة"
            body = f"تم رفض طلبك للإجازة من {self.request_date_from} إلى {self.request_date_to}"
            
            # Try to get rejection reason from report_note or use default
            reason = self.report_note or "لم يتم تحديد السبب"
            
            data = {
                'type': 'leave_rejected',
                'leave_id': str(self.id),
                'date_from': str(self.request_date_from),
                'date_to': str(self.request_date_to),
                'leave_type': self.holiday_status_id.name,
                'reason': reason,
                'number_of_days': str(self.number_of_days)
            }
            
            employee_user.send_fcm_notification(title, body, data)
            _logger.info(f'Leave rejection notification sent to employee {employee_user.name}')
        
        return result

    @api.model
    def approve_leave_with_notification(self, leave_id):
        """API method to approve leave and send notification - for mobile app"""
        try:
            leave = self.browse(leave_id)
            if leave.exists():
                leave.action_approve()
                return {'success': True, 'message': 'Leave approved and notification sent'}
            else:
                return {'success': False, 'message': 'Leave request not found'}
        except Exception as e:
            _logger.error(f'Error approving leave: {str(e)}')
            return {'success': False, 'message': str(e)}

    @api.model
    def reject_leave_with_notification(self, leave_id, reason=""):
        """API method to reject leave and send notification - for mobile app"""
        try:
            leave = self.browse(leave_id)
            if leave.exists():
                if reason:
                    leave.report_note = reason
                leave.action_refuse()
                return {'success': True, 'message': 'Leave rejected and notification sent'}
            else:
                return {'success': False, 'message': 'Leave request not found'}
        except Exception as e:
            _logger.error(f'Error rejecting leave: {str(e)}')
            return {'success': False, 'message': str(e)}
