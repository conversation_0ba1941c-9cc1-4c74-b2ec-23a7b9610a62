/// إعدادات الأصول والصور
/// يحتوي على مسارات جميع الصور والأصول المستخدمة في التطبيق
class AssetsConfig {
  // ==================== المسارات الأساسية ====================

  static const String _imagesPath = 'assets/images';
  static const String _logosPath = '$_imagesPath/logos';
  static const String _iconsPath = '$_imagesPath/icons';
  static const String _backgroundsPath = '$_imagesPath/backgrounds';
  static const String _illustrationsPath = '$_imagesPath/illustrations';

  // ==================== الشعارات ====================

  /// شعار التطبيق الرئيسي (أعلى الشاشة)
  static const String appLogo = '$_logosPath/app_logo.png';

  /// شعار التطبيق السفلي (أسفل الشاشة)
  static const String appLogoDown = '$_logosPath/app_logo_down.png';

  /// شعار الشركة
  static const String companyLogo = '$_logosPath/company_logo.png';

  /// أيقونة التطبيق
  static const String appIcon = '$_logosPath/app_icon.png';

  // ==================== الأيقونات ====================

  /// أيقونة البصمة المخصصة
  static const String fingerprintIcon = '$_iconsPath/fingerprint.png';

  /// أيقونة الإعدادات
  static const String settingsIcon = '$_iconsPath/settings.png';

  /// أيقونة الإجازات
  static const String leaveIcon = '$_iconsPath/leave.png';

  /// أيقونة الموافقات
  static const String approvalIcon = '$_iconsPath/approval.png';

  // ==================== الخلفيات ====================

  /// خلفية شاشة تسجيل الدخول
  static const String loginBackground = '$_backgroundsPath/login_bg.png';

  /// خلفية الشاشة الرئيسية
  static const String mainBackground = '$_backgroundsPath/main_bg.png';

  /// نمط الخلفية
  static const String patternBackground = '$_backgroundsPath/pattern.png';

  // ==================== الرسوم التوضيحية ====================

  /// رسم توضيحي للحالة الفارغة
  static const String emptyStateIllustration =
      '$_illustrationsPath/empty_state.png';

  /// رسم توضيحي للخطأ
  static const String errorIllustration = '$_illustrationsPath/error.png';

  /// رسم توضيحي للنجاح
  static const String successIllustration = '$_illustrationsPath/success.png';

  /// رسم توضيحي للتحميل
  static const String loadingIllustration = '$_illustrationsPath/loading.png';

  // ==================== دوال مساعدة ====================

  /// التحقق من وجود صورة
  static bool assetExists(String assetPath) {
    try {
      // يمكن استخدام هذه الدالة للتحقق من وجود الصورة
      return true; // مؤقتاً - يمكن تحسينها لاحقاً
    } catch (e) {
      return false;
    }
  }

  /// الحصول على مسار صورة بحجم محدد
  static String getImageWithSize(String basePath, String size) {
    final extension = basePath.split('.').last;
    final pathWithoutExtension = basePath.replaceAll('.$extension', '');
    return '${pathWithoutExtension}_$size.$extension';
  }

  /// الحصول على صورة بدقة عالية
  static String getHighResImage(String basePath) {
    return getImageWithSize(basePath, '2x');
  }

  /// الحصول على صورة بدقة فائقة
  static String getExtraHighResImage(String basePath) {
    return getImageWithSize(basePath, '3x');
  }
}

/// أمثلة على الاستخدام:
/// 
/// ```dart
/// // استخدام شعار التطبيق
/// Image.asset(AssetsConfig.appLogo)
/// 
/// // استخدام أيقونة البصمة
/// Image.asset(AssetsConfig.fingerprintIcon)
/// 
/// // استخدام خلفية تسجيل الدخول
/// Image.asset(AssetsConfig.loginBackground)
/// 
/// // استخدام صورة بدقة عالية
/// Image.asset(AssetsConfig.getHighResImage(AssetsConfig.appLogo))
/// ```
