import 'dart:ui';
import 'package:shared_preferences/shared_preferences.dart';

/// خدمة إدارة اللغات
class LanguageService {
  static const String _languageKey = 'selected_language';
  
  /// اللغات المدعومة
  static const List<LanguageModel> supportedLanguages = [
    LanguageModel(
      code: 'ar',
      name: 'العربية',
      englishName: 'Arabic',
      flag: '🇸🇦',
      isRTL: true,
    ),
    LanguageModel(
      code: 'en',
      name: 'English',
      englishName: 'English',
      flag: '🇺🇸',
      isRTL: false,
    ),
    LanguageModel(
      code: 'fr',
      name: 'Français',
      englishName: 'French',
      flag: '🇫🇷',
      isRTL: false,
    ),
  ];

  /// الحصول على اللغة المحفوظة
  static Future<String> getSavedLanguage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString(_languageKey) ?? 'ar'; // العربية افتراضياً
    } catch (e) {
      return 'ar'; // العربية في حالة الخطأ
    }
  }

  /// حفظ اللغة المختارة
  static Future<bool> saveLanguage(String languageCode) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return await prefs.setString(_languageKey, languageCode);
    } catch (e) {
      return false;
    }
  }

  /// الحصول على Locale من كود اللغة
  static Locale getLocaleFromCode(String languageCode) {
    return Locale(languageCode);
  }

  /// الحصول على معلومات اللغة من الكود
  static LanguageModel? getLanguageFromCode(String code) {
    try {
      return supportedLanguages.firstWhere((lang) => lang.code == code);
    } catch (e) {
      return supportedLanguages.first; // العربية افتراضياً
    }
  }

  /// التحقق من دعم اللغة
  static bool isLanguageSupported(String languageCode) {
    return supportedLanguages.any((lang) => lang.code == languageCode);
  }

  /// الحصول على اللغة الافتراضية للنظام
  static String getSystemLanguage() {
    final systemLocale = PlatformDispatcher.instance.locale;
    final systemLanguageCode = systemLocale.languageCode;
    
    // التحقق من دعم لغة النظام
    if (isLanguageSupported(systemLanguageCode)) {
      return systemLanguageCode;
    }
    
    return 'ar'; // العربية افتراضياً
  }

  /// تغيير اللغة
  static Future<bool> changeLanguage(String languageCode) async {
    if (!isLanguageSupported(languageCode)) {
      return false;
    }

    return await saveLanguage(languageCode);
  }

  /// الحصول على اتجاه النص للغة
  static TextDirection getTextDirection(String languageCode) {
    final language = getLanguageFromCode(languageCode);
    return language?.isRTL == true ? TextDirection.rtl : TextDirection.ltr;
  }

  /// الحصول على اسم اللغة بلغتها الأصلية
  static String getLanguageNativeName(String languageCode) {
    final language = getLanguageFromCode(languageCode);
    return language?.name ?? 'العربية';
  }

  /// الحصول على اسم اللغة بالإنجليزية
  static String getLanguageEnglishName(String languageCode) {
    final language = getLanguageFromCode(languageCode);
    return language?.englishName ?? 'Arabic';
  }

  /// الحصول على علم اللغة
  static String getLanguageFlag(String languageCode) {
    final language = getLanguageFromCode(languageCode);
    return language?.flag ?? '🇸🇦';
  }
}

/// نموذج اللغة
class LanguageModel {
  final String code;
  final String name;
  final String englishName;
  final String flag;
  final bool isRTL;

  const LanguageModel({
    required this.code,
    required this.name,
    required this.englishName,
    required this.flag,
    required this.isRTL,
  });

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LanguageModel && other.code == code;
  }

  @override
  int get hashCode => code.hashCode;

  @override
  String toString() {
    return 'LanguageModel(code: $code, name: $name, englishName: $englishName, flag: $flag, isRTL: $isRTL)';
  }

  /// تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'code': code,
      'name': name,
      'englishName': englishName,
      'flag': flag,
      'isRTL': isRTL,
    };
  }

  /// إنشاء من Map
  factory LanguageModel.fromMap(Map<String, dynamic> map) {
    return LanguageModel(
      code: map['code'] ?? '',
      name: map['name'] ?? '',
      englishName: map['englishName'] ?? '',
      flag: map['flag'] ?? '',
      isRTL: map['isRTL'] ?? false,
    );
  }
}
