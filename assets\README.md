# مجلد الأصول (Assets)

هذا المجلد يحتوي على جميع الصور والأصول المستخدمة في التطبيق.

## 📁 البنية التنظيمية

### `images/`
المجلد الرئيسي للصور

#### `logos/`
- شعارات الشركة والتطبيق
- أيقونات التطبيق بأحجام مختلفة
- شعارات الشركاء

#### `icons/`
- أيقونات مخصصة للتطبيق
- أيقونات الميزات
- أيقونات الحالات

#### `backgrounds/`
- خلفيات الشاشات
- صور الخلفية للواجهات
- أنماط الخلفية

#### `illustrations/`
- رسوم توضيحية
- صور الحالات الفارغة
- رسوم الأخطاء والنجاح

## 🎯 كيفية الاستخدام

### إضافة صورة جديدة:
1. ضع الصورة في المجلد المناسب
2. استخدمها في الكود:
```dart
Image.asset('assets/images/logos/app_logo.png')
```

### أفضل الممارسات:
- استخدم أسماء وصفية للملفات
- احفظ الصور بصيغ مناسبة (PNG للشفافية، JPG للصور)
- استخدم أحجام مناسبة لتجنب بطء التطبيق
- اتبع نمط التسمية: `category_name_size.extension`

## 📐 أحجام الصور المقترحة

### الشعارات:
- **صغير:** 24x24, 32x32
- **متوسط:** 64x64, 128x128  
- **كبير:** 256x256, 512x512

### الأيقونات:
- **صغير:** 16x16, 24x24
- **متوسط:** 32x32, 48x48
- **كبير:** 64x64, 96x96

### الخلفيات:
- **موبايل:** 375x812, 414x896
- **تابلت:** 768x1024, 1024x768

## 🔧 ملاحظات تقنية

- جميع المجلدات مسجلة في `pubspec.yaml`
- يمكن إضافة مجلدات فرعية جديدة حسب الحاجة
- تأكد من تشغيل `flutter pub get` بعد إضافة صور جديدة
