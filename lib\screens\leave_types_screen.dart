import 'package:flutter/material.dart';
import '../services/odoo_service.dart';
import '../services/firebase_analytics_service.dart';
import '../services/performance_monitoring_service.dart';
import '../models/leave_type.dart';
import '../config/app_config.dart';
import 'leave_request_screen.dart';
import '../generated/l10n/app_localizations.dart';

/// شاشة أنواع الإجازات المصرفية الحديثة
class LeaveTypesScreen extends StatefulWidget {
  final OdooService odooService;
  final int uid;
  final String password;

  const LeaveTypesScreen({
    super.key,
    required this.odooService,
    required this.uid,
    required this.password,
  });

  @override
  State<LeaveTypesScreen> createState() => _LeaveTypesScreenState();
}

class _LeaveTypesScreenState extends State<LeaveTypesScreen>
    with TickerProviderStateMixin {
  List<LeaveType> _leaveTypes = [];
  bool _isLoading = true;
  String? _errorMessage;
  bool _disposed = false;
  String _loadingMessage = 'جاري تحميل أنواع الإجازات...';

  late AnimationController _fadeController;
  late AnimationController _listController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _listAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadLeaveTypes();

    // تتبع مشاهدة شاشة أنواع الإجازات
    WidgetsBinding.instance.addPostFrameCallback((_) {
      FirebaseAnalyticsService.logScreenView(
        screenName: 'leave_types_screen',
        screenClass: 'LeaveTypesScreen',
      );
    });
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _listController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    _listAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _listController, curve: Curves.easeOutCubic),
    );
  }

  @override
  void dispose() {
    _disposed = true;
    _fadeController.dispose();
    _listController.dispose();
    super.dispose();
  }

  /// تحميل أنواع الإجازات من Odoo (محسن للأداء)
  Future<void> _loadLeaveTypes() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
      _loadingMessage = 'جاري الاتصال بالخادم...';
    });

    try {
      // تحديث رسالة التحميل
      setState(() {
        _loadingMessage = 'جاري جلب أنواع الإجازات...';
      });

      // إضافة timeout لتجنب الانتظار الطويل مع تتبع الأداء
      final leaveTypesData =
          await PerformanceMonitoringService.traceDatabaseOperation(
            operationType: 'read',
            tableName: 'hr.leave.type',
            operation: () => widget.odooService
                .getAvailableLeaveTypesForEmployee(
                  uid: widget.uid,
                  password: widget.password,
                )
                .timeout(
                  const Duration(seconds: 30), // مهلة زمنية 30 ثانية
                  onTimeout: () {
                    throw Exception(
                      'انتهت مهلة تحميل البيانات. يرجى المحاولة مرة أخرى.',
                    );
                  },
                ),
          );

      if (!mounted) return; // التحقق من أن الشاشة ما زالت مفتوحة

      if (leaveTypesData != null) {
        // تتبع نجاح تحميل أنواع الإجازات
        await FirebaseAnalyticsService.logEvent(
          name: 'leave_types_loaded',
          parameters: {
            'types_count': leaveTypesData.length,
            'load_success': 'true',
          },
        );

        // تحديث رسالة التحميل
        setState(() {
          _loadingMessage = 'جاري معالجة البيانات...';
        });

        setState(() {
          _leaveTypes = leaveTypesData
              .map((data) => LeaveType.fromOdooData(data))
              .toList();
          _isLoading = false;
        });

        // تشغيل الحركات بعد تحميل البيانات
        if (!_disposed && mounted) {
          _fadeController.forward();
          Future.delayed(const Duration(milliseconds: 300), () {
            if (!_disposed && mounted) {
              _listController.forward();
            }
          });
        }
      } else {
        // تتبع فشل تحميل أنواع الإجازات
        await FirebaseAnalyticsService.logEvent(
          name: 'leave_types_load_failed',
          parameters: {'error_type': 'no_data', 'load_success': 'false'},
        );

        setState(() {
          _errorMessage = 'لم يتم العثور على أنواع إجازات متاحة';
          _isLoading = false;
        });
      }
    } catch (e) {
      if (!mounted) return; // التحقق من أن الشاشة ما زالت مفتوحة

      // تتبع خطأ تحميل أنواع الإجازات
      await FirebaseAnalyticsService.logError(
        errorType: 'leave_types_load_error',
        errorMessage: e.toString(),
        screenName: 'leave_types_screen',
        functionName: '_loadLeaveTypes',
      );

      setState(() {
        _errorMessage = 'حدث خطأ في تحميل البيانات: $e';
        _isLoading = false;
      });
    }
  }

  /// إعادة تحميل البيانات
  Future<void> _refreshData() async {
    // تتبع إعادة تحميل البيانات
    await FirebaseAnalyticsService.logEvent(
      name: 'data_refresh',
      parameters: {'screen': 'leave_types_screen'},
    );

    _fadeController.reset();
    _listController.reset();
    await _loadLeaveTypes();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConfig.lightGrayColorObj,
      body: Column(
        children: [
          _buildModernHeader(),
          Expanded(
            child: _isLoading
                ? _buildLoadingState()
                : _errorMessage != null
                ? _buildErrorState()
                : _buildLeaveTypesList(),
          ),
        ],
      ),
    );
  }

  /// بناء رأس الصفحة مع التصميم الجديد
  Widget _buildModernHeader() {
    return SafeArea(
      child: Padding(
        padding: EdgeInsets.all(AppConfig.spacing),
        child: Column(
          children: [
            // العنوان الرئيسي
            Container(
              width: double.infinity,
              padding: EdgeInsets.symmetric(
                vertical: AppConfig.largeSpacing,
                horizontal: AppConfig.spacing,
              ),
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [
                    Color(0xFF7DD3FC), // سماوي فاتح
                    Color(0xFF67E8F9), // سماوي متوسط
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(
                  AppConfig.largeBorderRadius,
                ),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFF7DD3FC).withValues(alpha: 0.3),
                    blurRadius: 15,
                    offset: const Offset(0, 8),
                  ),
                ],
              ),
              child: Text(
                AppLocalizations.of(context).selectleavetype,
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  color: AppConfig.darkTextColorObj,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء حالة التحميل
  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: EdgeInsets.all(AppConfig.largeSpacing),
            decoration: BoxDecoration(
              color: AppConfig.whiteColorObj,
              borderRadius: BorderRadius.circular(AppConfig.largeBorderRadius),
              boxShadow: [
                BoxShadow(
                  color: AppConfig.cardShadowColorObj,
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                ),
              ],
            ),
            child: Column(
              children: [
                CircularProgressIndicator(
                  color: AppConfig.primaryColorObj,
                  strokeWidth: 3,
                ),
                SizedBox(height: AppConfig.spacing),
                Text(
                  _loadingMessage,
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: AppConfig.darkTextColorObj,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء حالة الخطأ
  Widget _buildErrorState() {
    return Center(
      child: Container(
        margin: EdgeInsets.all(AppConfig.largeSpacing),
        padding: EdgeInsets.all(AppConfig.largeSpacing),
        decoration: BoxDecoration(
          color: AppConfig.whiteColorObj,
          borderRadius: BorderRadius.circular(AppConfig.largeBorderRadius),
          boxShadow: [
            BoxShadow(
              color: AppConfig.cardShadowColorObj,
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: EdgeInsets.all(AppConfig.spacing),
              decoration: BoxDecoration(
                color: AppConfig.errorColorObj.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppConfig.borderRadius),
              ),
              child: Icon(
                Icons.error_outline,
                color: AppConfig.errorColorObj,
                size: 48,
              ),
            ),
            SizedBox(height: AppConfig.spacing),
            Text(
              'حدث خطأ',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: AppConfig.darkTextColorObj,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: AppConfig.smallSpacing),
            Text(
              _errorMessage!,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppConfig.secondaryTextColorObj,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: AppConfig.largeSpacing),
            ElevatedButton.icon(
              onPressed: _refreshData,
              icon: const Icon(Icons.refresh),
              label: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء قائمة أنواع الإجازات
  Widget _buildLeaveTypesList() {
    if (_leaveTypes.isEmpty) {
      return Center(
        child: Container(
          margin: EdgeInsets.all(AppConfig.largeSpacing),
          padding: EdgeInsets.all(AppConfig.largeSpacing),
          decoration: BoxDecoration(
            color: AppConfig.whiteColorObj,
            borderRadius: BorderRadius.circular(AppConfig.largeBorderRadius),
            boxShadow: [
              BoxShadow(
                color: AppConfig.cardShadowColorObj,
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                padding: EdgeInsets.all(AppConfig.spacing),
                decoration: BoxDecoration(
                  color: AppConfig.secondaryTextColorObj.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(AppConfig.borderRadius),
                ),
                child: Icon(
                  Icons.event_busy,
                  color: AppConfig.secondaryTextColorObj,
                  size: 48,
                ),
              ),
              SizedBox(height: AppConfig.spacing),
              Text(
                'لا توجد أنواع إجازات',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  color: AppConfig.darkTextColorObj,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: AppConfig.smallSpacing),
              Text(
                'لم يتم العثور على أنواع إجازات متاحة',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppConfig.secondaryTextColorObj,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return FadeTransition(
      opacity: _fadeAnimation,
      child: RefreshIndicator(
        onRefresh: _refreshData,
        color: AppConfig.primaryColorObj,
        child: AnimatedBuilder(
          animation: _listAnimation,
          builder: (context, child) {
            return ListView.builder(
              physics: const AlwaysScrollableScrollPhysics(),
              padding: EdgeInsets.all(AppConfig.spacing),
              itemCount: _leaveTypes.length,
              itemBuilder: (context, index) {
                final leaveType = _leaveTypes[index];
                return SlideTransition(
                  position:
                      Tween<Offset>(
                        begin: const Offset(0, 0.3),
                        end: Offset.zero,
                      ).animate(
                        CurvedAnimation(
                          parent: _listController,
                          curve: Interval(
                            index * 0.1,
                            1.0,
                            curve: Curves.easeOutCubic,
                          ),
                        ),
                      ),
                  child: FadeTransition(
                    opacity: Tween<double>(begin: 0.0, end: 1.0).animate(
                      CurvedAnimation(
                        parent: _listController,
                        curve: Interval(
                          index * 0.1,
                          1.0,
                          curve: Curves.easeInOut,
                        ),
                      ),
                    ),
                    child: _buildLeaveTypeCard(leaveType, index),
                  ),
                );
              },
            );
          },
        ),
      ),
    );
  }

  /// بناء بطاقة نوع الإجازة بالتصميم الجديد
  Widget _buildLeaveTypeCard(LeaveType leaveType, int index) {
    // ألوان وتدرجات متنوعة للبطاقات مطابقة للتصميم
    final cardStyles = [
      {
        'background': const LinearGradient(
          colors: [Color(0xFFE0F7FA), Color(0xFFB2EBF2)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        'border': const Color(0xFF7DD3FC),
        'iconBg': const Color(0xFF7DD3FC),
        'iconColor': const Color(0xFF0891B2),
      },
      {
        'background': const LinearGradient(
          colors: [Color(0xFFFFF3E0), Color(0xFFFFE0B2)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        'border': const Color(0xFFFFB74D),
        'iconBg': const Color(0xFFFFB74D),
        'iconColor': const Color(0xFFE65100),
      },
      {
        'background': const LinearGradient(
          colors: [Color(0xFFE8F5E8), Color(0xFFC8E6C9)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        'border': const Color(0xFF81C784),
        'iconBg': const Color(0xFF81C784),
        'iconColor': const Color(0xFF2E7D32),
      },
      {
        'background': const LinearGradient(
          colors: [Color(0xFFF3E5F5), Color(0xFFE1BEE7)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        'border': const Color(0xFFBA68C8),
        'iconBg': const Color(0xFFBA68C8),
        'iconColor': const Color(0xFF6A1B9A),
      },
    ];

    final style = cardStyles[index % cardStyles.length];

    return Container(
      margin: EdgeInsets.only(bottom: AppConfig.spacing),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _navigateToLeaveRequest(leaveType),
          borderRadius: BorderRadius.circular(AppConfig.largeBorderRadius),
          child: Container(
            padding: EdgeInsets.all(AppConfig.largeSpacing),
            decoration: BoxDecoration(
              gradient: style['background'] as LinearGradient,
              borderRadius: BorderRadius.circular(AppConfig.largeBorderRadius),
              border: Border.all(color: style['border'] as Color, width: 2),
              boxShadow: [
                BoxShadow(
                  color: (style['border'] as Color).withValues(alpha: 0.2),
                  blurRadius: 15,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: Row(
              children: [
                // معلومات نوع الإجازة
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        leaveType.name,
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: AppConfig.darkTextColorObj,
                        ),
                      ),
                    ],
                  ),
                ),

                // أيقونة نوع الإجازة
                Container(
                  width: 70,
                  height: 70,
                  decoration: BoxDecoration(
                    color: style['iconBg'] as Color,
                    borderRadius: BorderRadius.circular(AppConfig.borderRadius),
                    boxShadow: [
                      BoxShadow(
                        color: (style['iconBg'] as Color).withValues(
                          alpha: 0.3,
                        ),
                        blurRadius: 10,
                        offset: const Offset(0, 5),
                      ),
                    ],
                  ),
                  child: Icon(
                    Icons.calendar_month,
                    color: style['iconColor'] as Color,
                    size: 32,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// التنقل إلى شاشة طلب الإجازة
  void _navigateToLeaveRequest(LeaveType leaveType) async {
    // تتبع النقر على نوع الإجازة
    await FirebaseAnalyticsService.logEvent(
      name: 'leave_type_selected',
      parameters: {'leave_type': leaveType.name, 'leave_type_id': leaveType.id},
    );

    if (!mounted) return;

    final result = await Navigator.push(
      context,
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            LeaveRequestScreen(
              odooService: widget.odooService,
              uid: widget.uid,
              password: widget.password,
              leaveType: leaveType,
            ),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          const begin = Offset(1.0, 0.0);
          const end = Offset.zero;
          const curve = Curves.easeInOutCubic;

          var tween = Tween(
            begin: begin,
            end: end,
          ).chain(CurveTween(curve: curve));

          return SlideTransition(
            position: animation.drive(tween),
            child: child,
          );
        },
        transitionDuration: const Duration(milliseconds: 300),
      ),
    );

    // إذا تم تقديم الطلب بنجاح، يمكن إضافة أي إجراء إضافي هنا
    if (result == true && mounted) {
      // يمكن إعادة تحميل البيانات أو عرض رسالة نجاح
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('تم تقديم طلب الإجازة بنجاح'),
          backgroundColor: AppConfig.successColorObj,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppConfig.borderRadius),
          ),
        ),
      );
    }
  }
}
