/// نموذج بيانات الإجازة العامة للشركة
class PublicHoliday {
  final int? id;
  final String name;
  final DateTime dateFrom;
  final DateTime dateTo;
  final int? calendarId;
  final String? calendarName;

  PublicHoliday({
    this.id,
    required this.name,
    required this.dateFrom,
    required this.dateTo,
    this.calendarId,
    this.calendarName,
  });

  /// إنشاء كائن PublicHoliday من البيانات المستلمة من Odoo
  factory PublicHoliday.fromOdooData(Map<String, dynamic> data) {
    // معالجة التواريخ بحذر
    DateTime dateFrom = DateTime.now();
    DateTime dateTo = DateTime.now();

    try {
      if (data['date_from'] != null) {
        dateFrom = DateTime.parse(data['date_from'] as String);
      }
    } catch (e) {
      // في حالة الخطأ، استخدم التاريخ الحالي
    }

    try {
      if (data['date_to'] != null) {
        dateTo = DateTime.parse(data['date_to'] as String);
      }
    } catch (e) {
      // في حالة الخطأ، استخدم التاريخ الحالي
    }

    // معالجة معرف جدول العمل
    int? calendarId;
    String? calendarName;
    
    if (data['calendar_id'] != null) {
      if (data['calendar_id'] is List) {
        final calendarData = data['calendar_id'] as List;
        if (calendarData.length >= 2) {
          calendarId = calendarData[0] as int;
          calendarName = calendarData[1] as String;
        }
      } else if (data['calendar_id'] is int) {
        calendarId = data['calendar_id'] as int;
      }
    }

    return PublicHoliday(
      id: data['id'] as int?,
      name: data['name'] as String? ?? 'إجازة عامة',
      dateFrom: dateFrom,
      dateTo: dateTo,
      calendarId: calendarId,
      calendarName: calendarName,
    );
  }

  /// تحويل الكائن إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'dateFrom': dateFrom.toIso8601String(),
      'dateTo': dateTo.toIso8601String(),
      'calendarId': calendarId,
      'calendarName': calendarName,
    };
  }

  /// التحقق من أن التاريخ المحدد يقع ضمن فترة الإجازة العامة
  bool containsDate(DateTime date) {
    final dateOnly = DateTime(date.year, date.month, date.day);
    final fromOnly = DateTime(dateFrom.year, dateFrom.month, dateFrom.day);
    final toOnly = DateTime(dateTo.year, dateTo.month, dateTo.day);
    
    return (dateOnly.isAfter(fromOnly) || dateOnly.isAtSameMomentAs(fromOnly)) &&
           (dateOnly.isBefore(toOnly) || dateOnly.isAtSameMomentAs(toOnly));
  }

  /// الحصول على عدد الأيام في الإجازة العامة
  int get numberOfDays {
    return dateTo.difference(dateFrom).inDays + 1;
  }

  /// تنسيق التاريخ للعرض
  String get formattedDateRange {
    if (dateFrom.day == dateTo.day && 
        dateFrom.month == dateTo.month && 
        dateFrom.year == dateTo.year) {
      // يوم واحد فقط
      return '${dateFrom.day}/${dateFrom.month}/${dateFrom.year}';
    } else {
      // فترة من عدة أيام
      return '${dateFrom.day}/${dateFrom.month}/${dateFrom.year} - ${dateTo.day}/${dateTo.month}/${dateTo.year}';
    }
  }

  @override
  String toString() {
    return 'PublicHoliday{id: $id, name: $name, dateFrom: $dateFrom, dateTo: $dateTo, calendarId: $calendarId}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PublicHoliday &&
        other.id == id &&
        other.name == name &&
        other.dateFrom == dateFrom &&
        other.dateTo == dateTo &&
        other.calendarId == calendarId;
  }

  @override
  int get hashCode {
    return Object.hash(id, name, dateFrom, dateTo, calendarId);
  }
}
