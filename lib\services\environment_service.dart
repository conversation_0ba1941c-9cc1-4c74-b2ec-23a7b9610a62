import 'dart:io';
import 'package:flutter/services.dart';

/// خدمة إدارة الإعدادات الآمنة للإنتاج
/// تقرأ الإعدادات من متغيرات البيئة أو ملف .env
class EnvironmentService {
  // مفاتيح متغيرات البيئة - إعدادات Odoo
  static const String _keyServerUrl = 'ODOO_SERVER_URL';
  static const String _keyDatabase = 'ODOO_DATABASE';
  static const String _keyApiKey = 'ODOO_API_KEY';

  // مفاتيح إعدادات الاتصال
  static const String _keyConnectionTimeout = 'CONNECTION_TIMEOUT';
  static const String _keyRequestTimeout = 'REQUEST_TIMEOUT';

  // مفاتيح إعدادات التطبيق
  static const String _keyAppName = 'APP_NAME';
  static const String _keyAppSubtitle = 'APP_SUBTITLE';
  static const String _keyAppVersion = 'APP_VERSION';

  // مفاتيح رسائل الخطأ
  static const String _keyConnectionErrorMessage = 'CONNECTION_ERROR_MESSAGE';
  static const String _keyAuthenticationErrorMessage =
      'AUTHENTICATION_ERROR_MESSAGE';
  static const String _keyNoEmployeeDataMessage = 'NO_EMPLOYEE_DATA_MESSAGE';
  static const String _keyGeneralErrorMessage = 'GENERAL_ERROR_MESSAGE';

  // مفاتيح الألوان
  static const String _keyPrimaryColor = 'PRIMARY_COLOR';
  static const String _keyWhiteColor = 'WHITE_COLOR';
  static const String _keyLightGrayColor = 'LIGHT_GRAY_COLOR';
  static const String _keyDarkTextColor = 'DARK_TEXT_COLOR';
  static const String _keySuccessColor = 'SUCCESS_COLOR';
  static const String _keyErrorColor = 'ERROR_COLOR';
  static const String _keyCardShadowColor = 'CARD_SHADOW_COLOR';
  static const String _keyDividerColor = 'DIVIDER_COLOR';
  static const String _keySecondaryTextColor = 'SECONDARY_TEXT_COLOR';

  // مفاتيح التدرجات اللونية
  static const String _keyPrimaryGradient = 'PRIMARY_GRADIENT';
  static const String _keySuccessGradient = 'SUCCESS_GRADIENT';
  static const String _keyCardGradient = 'CARD_GRADIENT';

  // مفاتيح أبعاد التصميم
  static const String _keyBorderRadius = 'BORDER_RADIUS';
  static const String _keyLargeBorderRadius = 'LARGE_BORDER_RADIUS';
  static const String _keyCardElevation = 'CARD_ELEVATION';
  static const String _keyButtonHeight = 'BUTTON_HEIGHT';
  static const String _keySpacing = 'SPACING';
  static const String _keyLargeSpacing = 'LARGE_SPACING';
  static const String _keySmallSpacing = 'SMALL_SPACING';

  // مفاتيح أحجام الخطوط
  static const String _keyHeadlineFontSize = 'HEADLINE_FONT_SIZE';
  static const String _keyTitleFontSize = 'TITLE_FONT_SIZE';
  static const String _keyBodyFontSize = 'BODY_FONT_SIZE';
  static const String _keyCaptionFontSize = 'CAPTION_FONT_SIZE';
  static const String _keySmallFontSize = 'SMALL_FONT_SIZE';

  // مفاتيح Certificate Pinning
  static const String _keyCertificatePinningEnabled =
      'CERTIFICATE_PINNING_ENABLED';
  static const String _keyCertificatePinningSha256 =
      'CERTIFICATE_PINNING_SHA256';
  static const String _keyCertificatePinningTimeout =
      'CERTIFICATE_PINNING_TIMEOUT';
  static const String _keyAllowSelfSignedCertificates =
      'ALLOW_SELF_SIGNED_CERTIFICATES';

  // مفاتيح إعدادات الجلسات
  static const String _keySessionTimeoutEnabled = 'SESSION_TIMEOUT_ENABLED';
  static const String _keySessionTimeoutMinutes = 'SESSION_TIMEOUT_MINUTES';
  static const String _keySessionWarningMinutes = 'SESSION_WARNING_MINUTES';
  static const String _keySessionWarningsEnabled = 'SESSION_WARNINGS_ENABLED';
  static const String _keyAutoLogoutEnabled = 'AUTO_LOGOUT_ENABLED';

  // مفاتيح إعدادات Firebase
  static const String _keyFirebaseEnabled = 'FIREBASE_ENABLED';
  static const String _keyFirebaseAnalyticsEnabled =
      'FIREBASE_ANALYTICS_ENABLED';
  static const String _keyFirebasePerformanceEnabled =
      'FIREBASE_PERFORMANCE_ENABLED';
  static const String _keyFirebaseCrashlyticsEnabled =
      'FIREBASE_CRASHLYTICS_ENABLED';
  static const String _keyFirebaseDebugMode = 'FIREBASE_DEBUG_MODE';

  // مفاتيح إعدادات مراقبة الأداء
  static const String _keyPerformanceMonitoringEnabled =
      'PERFORMANCE_MONITORING_ENABLED';
  static const String _keyPerformanceTraceNetworkRequests =
      'PERFORMANCE_TRACE_NETWORK_REQUESTS';
  static const String _keyPerformanceTraceScreenRendering =
      'PERFORMANCE_TRACE_SCREEN_RENDERING';
  static const String _keyPerformanceTraceUserInteractions =
      'PERFORMANCE_TRACE_USER_INTERACTIONS';

  // مفاتيح إعدادات Analytics
  static const String _keyAnalyticsCollectUserProperties =
      'ANALYTICS_COLLECT_USER_PROPERTIES';
  static const String _keyAnalyticsCollectScreenViews =
      'ANALYTICS_COLLECT_SCREEN_VIEWS';
  static const String _keyAnalyticsCollectCustomEvents =
      'ANALYTICS_COLLECT_CUSTOM_EVENTS';
  static const String _keyAnalyticsSessionTimeoutDuration =
      'ANALYTICS_SESSION_TIMEOUT_DURATION';

  // القيم الافتراضية للإنتاج - يجب تعيين القيم الحقيقية في ملف .env
  static const String _defaultServerUrl = '';
  static const String _defaultDatabase = '';

  // إعدادات مخبأة
  static Map<String, String>? _cachedConfig;
  static bool _isInitialized = false;

  /// تهيئة خدمة البيئة
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      _cachedConfig = await _loadConfiguration();
      _isInitialized = true;
    } catch (e) {
      // استخدام الإعدادات الافتراضية في حالة الخطأ
      _cachedConfig = _getDefaultConfiguration();
      _isInitialized = true;
    }
  }

  /// تحميل الإعدادات من مصادر مختلفة
  static Future<Map<String, String>> _loadConfiguration() async {
    final config = <String, String>{};

    // 1. قراءة من متغيرات البيئة
    config.addAll(_loadFromEnvironmentVariables());

    // 2. قراءة من ملف الإعدادات المحلي (إن وجد)
    final localConfig = await _loadFromLocalConfigFile();
    config.addAll(localConfig);

    // 3. إضافة القيم الافتراضية للمفاتيح المفقودة
    _addDefaultValues(config);

    return config;
  }

  /// قراءة من متغيرات البيئة
  static Map<String, String> _loadFromEnvironmentVariables() {
    final config = <String, String>{};

    final serverUrl = Platform.environment[_keyServerUrl];
    final database = Platform.environment[_keyDatabase];
    final apiKey = Platform.environment[_keyApiKey];

    if (serverUrl != null) config['serverUrl'] = serverUrl;
    if (database != null) config['database'] = database;
    if (apiKey != null) config['apiKey'] = apiKey;

    return config;
  }

  /// قراءة من ملف الإعدادات المحلي
  static Future<Map<String, String>> _loadFromLocalConfigFile() async {
    final config = <String, String>{};

    try {
      // محاولة قراءة ملف .env من assets أولاً (للتطبيق المبني)
      try {
        final content = await rootBundle.loadString('.env');
        config.addAll(_parseEnvFile(content));
      } catch (assetError) {
        // إذا فشل قراءة من assets، جرب قراءة من نظام الملفات (للتطوير)
        final envFile = File('.env');
        if (await envFile.exists()) {
          final content = await envFile.readAsString();
          config.addAll(_parseEnvFile(content));
        }
      }
    } catch (e) {
      // تجاهل أخطاء قراءة ملف .env
    }

    return config;
  }

  /// تحليل محتوى ملف .env
  static Map<String, String> _parseEnvFile(String content) {
    final config = <String, String>{};
    final lines = content.split('\n');

    for (final line in lines) {
      final trimmedLine = line.trim();
      if (trimmedLine.isEmpty || trimmedLine.startsWith('#')) {
        continue; // تجاهل الأسطر الفارغة والتعليقات
      }

      final parts = trimmedLine.split('=');
      if (parts.length >= 2) {
        final key = parts[0].trim();
        final value = parts.sublist(1).join('=').trim();

        // إزالة علامات الاقتباس إن وجدت
        String cleanValue = value;
        if (cleanValue.startsWith('"') && cleanValue.endsWith('"')) {
          cleanValue = cleanValue.substring(1, cleanValue.length - 1);
        } else if (cleanValue.startsWith("'") && cleanValue.endsWith("'")) {
          cleanValue = cleanValue.substring(1, cleanValue.length - 1);
        }

        // تحويل مفاتيح البيئة إلى مفاتيح داخلية
        switch (key) {
          // إعدادات Odoo
          case _keyServerUrl:
            config['serverUrl'] = cleanValue;
            break;
          case _keyDatabase:
            config['database'] = cleanValue;
            break;
          case _keyApiKey:
            config['apiKey'] = cleanValue;
            break;

          // إعدادات الاتصال
          case _keyConnectionTimeout:
            config[_keyConnectionTimeout] = cleanValue;
            break;
          case _keyRequestTimeout:
            config[_keyRequestTimeout] = cleanValue;
            break;

          // إعدادات التطبيق
          case _keyAppName:
            config[_keyAppName] = cleanValue;
            break;
          case _keyAppSubtitle:
            config[_keyAppSubtitle] = cleanValue;
            break;
          case _keyAppVersion:
            config[_keyAppVersion] = cleanValue;
            break;

          // رسائل الخطأ
          case _keyConnectionErrorMessage:
            config[_keyConnectionErrorMessage] = cleanValue;
            break;
          case _keyAuthenticationErrorMessage:
            config[_keyAuthenticationErrorMessage] = cleanValue;
            break;
          case _keyNoEmployeeDataMessage:
            config[_keyNoEmployeeDataMessage] = cleanValue;
            break;
          case _keyGeneralErrorMessage:
            config[_keyGeneralErrorMessage] = cleanValue;
            break;

          // الألوان
          case _keyPrimaryColor:
            config[_keyPrimaryColor] = cleanValue;
            break;
          case _keyWhiteColor:
            config[_keyWhiteColor] = cleanValue;
            break;
          case _keyLightGrayColor:
            config[_keyLightGrayColor] = cleanValue;
            break;
          case _keyDarkTextColor:
            config[_keyDarkTextColor] = cleanValue;
            break;
          case _keySuccessColor:
            config[_keySuccessColor] = cleanValue;
            break;
          case _keyErrorColor:
            config[_keyErrorColor] = cleanValue;
            break;
          case _keyCardShadowColor:
            config[_keyCardShadowColor] = cleanValue;
            break;
          case _keyDividerColor:
            config[_keyDividerColor] = cleanValue;
            break;
          case _keySecondaryTextColor:
            config[_keySecondaryTextColor] = cleanValue;
            break;

          // التدرجات اللونية
          case _keyPrimaryGradient:
            config[_keyPrimaryGradient] = cleanValue;
            break;
          case _keySuccessGradient:
            config[_keySuccessGradient] = cleanValue;
            break;
          case _keyCardGradient:
            config[_keyCardGradient] = cleanValue;
            break;

          // أبعاد التصميم
          case _keyBorderRadius:
            config[_keyBorderRadius] = cleanValue;
            break;
          case _keyLargeBorderRadius:
            config[_keyLargeBorderRadius] = cleanValue;
            break;
          case _keyCardElevation:
            config[_keyCardElevation] = cleanValue;
            break;
          case _keyButtonHeight:
            config[_keyButtonHeight] = cleanValue;
            break;
          case _keySpacing:
            config[_keySpacing] = cleanValue;
            break;
          case _keyLargeSpacing:
            config[_keyLargeSpacing] = cleanValue;
            break;
          case _keySmallSpacing:
            config[_keySmallSpacing] = cleanValue;
            break;

          // أحجام الخطوط
          case _keyHeadlineFontSize:
            config[_keyHeadlineFontSize] = cleanValue;
            break;
          case _keyTitleFontSize:
            config[_keyTitleFontSize] = cleanValue;
            break;
          case _keyBodyFontSize:
            config[_keyBodyFontSize] = cleanValue;
            break;
          case _keyCaptionFontSize:
            config[_keyCaptionFontSize] = cleanValue;
            break;
          case _keySmallFontSize:
            config[_keySmallFontSize] = cleanValue;
            break;

          // Certificate Pinning
          case _keyCertificatePinningEnabled:
            config[_keyCertificatePinningEnabled] = cleanValue;
            break;
          case _keyCertificatePinningSha256:
            config[_keyCertificatePinningSha256] = cleanValue;
            break;
          case _keyCertificatePinningTimeout:
            config[_keyCertificatePinningTimeout] = cleanValue;
            break;
          case _keyAllowSelfSignedCertificates:
            config[_keyAllowSelfSignedCertificates] = cleanValue;
            break;

          // إعدادات الجلسات
          case _keySessionTimeoutEnabled:
            config[_keySessionTimeoutEnabled] = cleanValue;
            break;
          case _keySessionTimeoutMinutes:
            config[_keySessionTimeoutMinutes] = cleanValue;
            break;
          case _keySessionWarningMinutes:
            config[_keySessionWarningMinutes] = cleanValue;
            break;
          case _keySessionWarningsEnabled:
            config[_keySessionWarningsEnabled] = cleanValue;
            break;
          case _keyAutoLogoutEnabled:
            config[_keyAutoLogoutEnabled] = cleanValue;
            break;

          // إعدادات Firebase
          case _keyFirebaseEnabled:
            config[_keyFirebaseEnabled] = cleanValue;
            break;
          case _keyFirebaseAnalyticsEnabled:
            config[_keyFirebaseAnalyticsEnabled] = cleanValue;
            break;
          case _keyFirebasePerformanceEnabled:
            config[_keyFirebasePerformanceEnabled] = cleanValue;
            break;
          case _keyFirebaseCrashlyticsEnabled:
            config[_keyFirebaseCrashlyticsEnabled] = cleanValue;
            break;
          case _keyFirebaseDebugMode:
            config[_keyFirebaseDebugMode] = cleanValue;
            break;

          // إعدادات مراقبة الأداء
          case _keyPerformanceMonitoringEnabled:
            config[_keyPerformanceMonitoringEnabled] = cleanValue;
            break;
          case _keyPerformanceTraceNetworkRequests:
            config[_keyPerformanceTraceNetworkRequests] = cleanValue;
            break;
          case _keyPerformanceTraceScreenRendering:
            config[_keyPerformanceTraceScreenRendering] = cleanValue;
            break;
          case _keyPerformanceTraceUserInteractions:
            config[_keyPerformanceTraceUserInteractions] = cleanValue;
            break;

          // إعدادات Analytics
          case _keyAnalyticsCollectUserProperties:
            config[_keyAnalyticsCollectUserProperties] = cleanValue;
            break;
          case _keyAnalyticsCollectScreenViews:
            config[_keyAnalyticsCollectScreenViews] = cleanValue;
            break;
          case _keyAnalyticsCollectCustomEvents:
            config[_keyAnalyticsCollectCustomEvents] = cleanValue;
            break;
          case _keyAnalyticsSessionTimeoutDuration:
            config[_keyAnalyticsSessionTimeoutDuration] = cleanValue;
            break;
        }
      }
    }

    return config;
  }

  /// إضافة القيم الافتراضية للمفاتيح المفقودة
  static void _addDefaultValues(Map<String, String> config) {
    config.putIfAbsent('serverUrl', () => _defaultServerUrl);
    config.putIfAbsent('database', () => _defaultDatabase);
    config.putIfAbsent('apiKey', () => '');
  }

  /// الحصول على الإعدادات الافتراضية
  static Map<String, String> _getDefaultConfiguration() {
    return {
      'serverUrl': _defaultServerUrl,
      'database': _defaultDatabase,
      'apiKey': '',
    };
  }

  /// الحصول على رابط الخادم
  static String getServerUrl() {
    _ensureInitialized();
    return _cachedConfig!['serverUrl'] ?? _defaultServerUrl;
  }

  /// الحصول على اسم قاعدة البيانات
  static String getDatabase() {
    _ensureInitialized();
    return _cachedConfig!['database'] ?? _defaultDatabase;
  }

  /// الحصول على مفتاح API (إن وجد)
  static String getApiKey() {
    _ensureInitialized();
    return _cachedConfig!['apiKey'] ?? '';
  }

  /// الحصول على جميع الإعدادات
  static Map<String, String> getAllSettings() {
    _ensureInitialized();
    return Map<String, String>.from(_cachedConfig!);
  }

  /// إعادة تحميل الإعدادات
  static Future<void> reload() async {
    _isInitialized = false;
    _cachedConfig = null;
    await initialize();
  }

  /// التحقق من صحة الإعدادات
  static bool validateConfiguration() {
    try {
      final serverUrl = getServerUrl();
      final database = getDatabase();

      // التحقق من أن القيم ليست فارغة
      if (serverUrl.isEmpty || database.isEmpty) {
        return false;
      }

      // التحقق من صحة رابط الخادم
      final uri = Uri.tryParse(serverUrl);
      if (serverUrl.isEmpty || uri == null || !uri.hasScheme) {
        return false;
      }

      // التحقق من صحة اسم قاعدة البيانات
      if (database.isEmpty) {
        return false;
      }

      return true;
    } catch (e) {
      return false;
    }
  }

  /// التحقق من وجود قيم وهمية في الإعدادات
  static bool hasDummyValues() {
    try {
      final serverUrl = getServerUrl();
      final database = getDatabase();

      return serverUrl.isEmpty || database.isEmpty;
    } catch (e) {
      return true; // في حالة الخطأ، اعتبر أن هناك قيم وهمية
    }
  }

  /// الحصول على رسالة تحذيرية إذا كانت الإعدادات تحتوي على قيم وهمية
  static String? getDummyValuesWarning() {
    if (hasDummyValues()) {
      return 'تحذير: الإعدادات تحتوي على قيم وهمية. يرجى التأكد من وجود ملف .env مع القيم الصحيحة.';
    }
    return null;
  }

  /// الحصول على حالة تفعيل Certificate Pinning
  static bool getCertificatePinningEnabled() {
    _ensureInitialized();
    final value = _cachedConfig![_keyCertificatePinningEnabled] ?? 'true';
    return value.toLowerCase() == 'true';
  }

  /// الحصول على SHA-256 fingerprint للشهادة المثبتة
  static String getCertificatePinningSha256() {
    _ensureInitialized();
    return _cachedConfig![_keyCertificatePinningSha256] ?? '';
  }

  /// الحصول على مهلة Certificate Pinning
  static int getCertificatePinningTimeout() {
    _ensureInitialized();
    final value = _cachedConfig![_keyCertificatePinningTimeout] ?? '10';
    return int.tryParse(value) ?? 10;
  }

  /// الحصول على حالة تفعيل مهلة الجلسات
  static bool getSessionTimeoutEnabled() {
    _ensureInitialized();
    final value = _cachedConfig![_keySessionTimeoutEnabled] ?? 'true';
    return value.toLowerCase() == 'true';
  }

  /// الحصول على مدة مهلة الجلسة بالدقائق
  static int getSessionTimeoutMinutes() {
    _ensureInitialized();
    final value = _cachedConfig![_keySessionTimeoutMinutes] ?? '30';
    return int.tryParse(value) ?? 30;
  }

  /// الحصول على مدة التنبيه قبل انتهاء الجلسة بالدقائق
  static int getSessionWarningMinutes() {
    _ensureInitialized();
    final value = _cachedConfig![_keySessionWarningMinutes] ?? '5';
    return int.tryParse(value) ?? 5;
  }

  /// الحصول على حالة تفعيل تسجيل الخروج التلقائي
  static bool getAutoLogoutEnabled() {
    _ensureInitialized();
    final value = _cachedConfig![_keyAutoLogoutEnabled] ?? 'true';
    return value.toLowerCase() == 'true';
  }

  /// الحصول على حالة تفعيل تنبيهات انتهاء الجلسة
  static bool getSessionWarningsEnabled() {
    _ensureInitialized();
    final value = _cachedConfig![_keySessionWarningsEnabled] ?? 'true';
    return value.toLowerCase() == 'true';
  }

  /// الحصول على حالة السماح بالشهادات الموقعة ذاتياً
  static bool getAllowSelfSignedCertificates() {
    _ensureInitialized();
    final value = _cachedConfig![_keyAllowSelfSignedCertificates] ?? 'false';
    return value.toLowerCase() == 'true';
  }

  // ========================================
  // دوال قراءة إعدادات الاتصال
  // ========================================

  /// الحصول على مهلة الاتصال بالثواني
  static int getConnectionTimeout() {
    _ensureInitialized();
    final value = _cachedConfig![_keyConnectionTimeout] ?? '30';
    return int.tryParse(value) ?? 30;
  }

  /// الحصول على مهلة الطلب بالثواني
  static int getRequestTimeout() {
    _ensureInitialized();
    final value = _cachedConfig![_keyRequestTimeout] ?? '15';
    return int.tryParse(value) ?? 15;
  }

  // ========================================
  // دوال قراءة إعدادات التطبيق
  // ========================================

  /// الحصول على اسم التطبيق
  static String getAppName() {
    _ensureInitialized();
    return _cachedConfig![_keyAppName] ?? 'بنك الموظفين';
  }

  /// الحصول على العنوان الفرعي للتطبيق
  static String getAppSubtitle() {
    _ensureInitialized();
    return _cachedConfig![_keyAppSubtitle] ?? 'نظام إدارة الموظفين المصرفي';
  }

  /// الحصول على إصدار التطبيق
  static String getAppVersion() {
    _ensureInitialized();
    return _cachedConfig![_keyAppVersion] ?? '1.0.0';
  }

  // ========================================
  // دوال قراءة رسائل الخطأ
  // ========================================

  /// الحصول على رسالة خطأ الاتصال
  static String getConnectionErrorMessage() {
    _ensureInitialized();
    return _cachedConfig![_keyConnectionErrorMessage] ??
        'خطأ في الاتصال بالخادم';
  }

  /// الحصول على رسالة خطأ المصادقة
  static String getAuthenticationErrorMessage() {
    _ensureInitialized();
    return _cachedConfig![_keyAuthenticationErrorMessage] ??
        'فشل في تسجيل الدخول';
  }

  /// الحصول على رسالة عدم وجود بيانات الموظف
  static String getNoEmployeeDataMessage() {
    _ensureInitialized();
    return _cachedConfig![_keyNoEmployeeDataMessage] ??
        'لم يتم العثور على بيانات الموظف';
  }

  /// الحصول على رسالة الخطأ العام
  static String getGeneralErrorMessage() {
    _ensureInitialized();
    return _cachedConfig![_keyGeneralErrorMessage] ?? 'حدث خطأ غير متوقع';
  }

  // ========================================
  // دوال قراءة الألوان
  // ========================================

  /// الحصول على اللون الأساسي (hex بدون #)
  static String getPrimaryColorHex() {
    _ensureInitialized();
    return _cachedConfig![_keyPrimaryColor] ?? '3F91EB';
  }

  /// الحصول على اللون الأساسي كرقم
  static int getPrimaryColor() {
    final hex = getPrimaryColorHex();
    return int.parse('0xFF$hex');
  }

  /// الحصول على اللون الأبيض كرقم
  static int getWhiteColor() {
    _ensureInitialized();
    final hex = _cachedConfig![_keyWhiteColor] ?? 'FFFFFF';
    return int.parse('0xFF$hex');
  }

  /// الحصول على اللون الرمادي الفاتح كرقم
  static int getLightGrayColor() {
    _ensureInitialized();
    final hex = _cachedConfig![_keyLightGrayColor] ?? 'F5F7FA';
    return int.parse('0xFF$hex');
  }

  /// الحصول على لون النص الداكن كرقم
  static int getDarkTextColor() {
    _ensureInitialized();
    final hex = _cachedConfig![_keyDarkTextColor] ?? '1A1A1A';
    return int.parse('0xFF$hex');
  }

  /// الحصول على لون النجاح كرقم
  static int getSuccessColor() {
    _ensureInitialized();
    final hex = _cachedConfig![_keySuccessColor] ?? '28C76F';
    return int.parse('0xFF$hex');
  }

  /// الحصول على لون الخطأ كرقم
  static int getErrorColor() {
    _ensureInitialized();
    final hex = _cachedConfig![_keyErrorColor] ?? 'EA5455';
    return int.parse('0xFF$hex');
  }

  /// الحصول على لون ظل البطاقة كرقم
  static int getCardShadowColor() {
    _ensureInitialized();
    final hex = _cachedConfig![_keyCardShadowColor] ?? '1A000000';
    return int.parse('0x$hex');
  }

  /// الحصول على لون الفاصل كرقم
  static int getDividerColor() {
    _ensureInitialized();
    final hex = _cachedConfig![_keyDividerColor] ?? 'E8E8E8';
    return int.parse('0xFF$hex');
  }

  /// الحصول على لون النص الثانوي كرقم
  static int getSecondaryTextColor() {
    _ensureInitialized();
    final hex = _cachedConfig![_keySecondaryTextColor] ?? '6B7280';
    return int.parse('0xFF$hex');
  }

  // ========================================
  // دوال قراءة التدرجات اللونية
  // ========================================

  /// الحصول على التدرج الأساسي كقائمة ألوان
  static List<int> getPrimaryGradient() {
    _ensureInitialized();
    final value = _cachedConfig![_keyPrimaryGradient] ?? '3F91EB,2563EB';
    return value
        .split(',')
        .map((hex) => int.parse('0xFF${hex.trim()}'))
        .toList();
  }

  /// الحصول على تدرج النجاح كقائمة ألوان
  static List<int> getSuccessGradient() {
    _ensureInitialized();
    final value = _cachedConfig![_keySuccessGradient] ?? '28C76F,10B981';
    return value
        .split(',')
        .map((hex) => int.parse('0xFF${hex.trim()}'))
        .toList();
  }

  /// الحصول على تدرج البطاقة كقائمة ألوان
  static List<int> getCardGradient() {
    _ensureInitialized();
    final value = _cachedConfig![_keyCardGradient] ?? 'FFFFFF,F8FAFC';
    return value
        .split(',')
        .map((hex) => int.parse('0xFF${hex.trim()}'))
        .toList();
  }

  // ========================================
  // دوال قراءة أبعاد التصميم
  // ========================================

  /// الحصول على نصف قطر الحدود
  static double getBorderRadius() {
    _ensureInitialized();
    final value = _cachedConfig![_keyBorderRadius] ?? '16.0';
    return double.tryParse(value) ?? 16.0;
  }

  /// الحصول على نصف قطر الحدود الكبير
  static double getLargeBorderRadius() {
    _ensureInitialized();
    final value = _cachedConfig![_keyLargeBorderRadius] ?? '24.0';
    return double.tryParse(value) ?? 24.0;
  }

  /// الحصول على ارتفاع البطاقة
  static double getCardElevation() {
    _ensureInitialized();
    final value = _cachedConfig![_keyCardElevation] ?? '4.0';
    return double.tryParse(value) ?? 4.0;
  }

  /// الحصول على ارتفاع الزر
  static double getButtonHeight() {
    _ensureInitialized();
    final value = _cachedConfig![_keyButtonHeight] ?? '56.0';
    return double.tryParse(value) ?? 56.0;
  }

  /// الحصول على المسافة الأساسية
  static double getSpacing() {
    _ensureInitialized();
    final value = _cachedConfig![_keySpacing] ?? '16.0';
    return double.tryParse(value) ?? 16.0;
  }

  /// الحصول على المسافة الكبيرة
  static double getLargeSpacing() {
    _ensureInitialized();
    final value = _cachedConfig![_keyLargeSpacing] ?? '24.0';
    return double.tryParse(value) ?? 24.0;
  }

  /// الحصول على المسافة الصغيرة
  static double getSmallSpacing() {
    _ensureInitialized();
    final value = _cachedConfig![_keySmallSpacing] ?? '8.0';
    return double.tryParse(value) ?? 8.0;
  }

  // ========================================
  // دوال قراءة أحجام الخطوط
  // ========================================

  /// الحصول على حجم خط العنوان الرئيسي
  static double getHeadlineFontSize() {
    _ensureInitialized();
    final value = _cachedConfig![_keyHeadlineFontSize] ?? '28.0';
    return double.tryParse(value) ?? 28.0;
  }

  /// الحصول على حجم خط العنوان
  static double getTitleFontSize() {
    _ensureInitialized();
    final value = _cachedConfig![_keyTitleFontSize] ?? '22.0';
    return double.tryParse(value) ?? 22.0;
  }

  /// الحصول على حجم خط النص الأساسي
  static double getBodyFontSize() {
    _ensureInitialized();
    final value = _cachedConfig![_keyBodyFontSize] ?? '16.0';
    return double.tryParse(value) ?? 16.0;
  }

  /// الحصول على حجم خط التسمية التوضيحية
  static double getCaptionFontSize() {
    _ensureInitialized();
    final value = _cachedConfig![_keyCaptionFontSize] ?? '14.0';
    return double.tryParse(value) ?? 14.0;
  }

  /// الحصول على حجم الخط الصغير
  static double getSmallFontSize() {
    _ensureInitialized();
    final value = _cachedConfig![_keySmallFontSize] ?? '12.0';
    return double.tryParse(value) ?? 12.0;
  }

  // ========================================
  // دوال قراءة إعدادات Firebase
  // ========================================

  /// التحقق من تفعيل Firebase
  static bool isFirebaseEnabled() {
    _ensureInitialized();
    final value = _cachedConfig![_keyFirebaseEnabled] ?? 'true';
    return value.toLowerCase() == 'true';
  }

  /// التحقق من تفعيل Firebase Analytics
  static bool isFirebaseAnalyticsEnabled() {
    _ensureInitialized();
    final value = _cachedConfig![_keyFirebaseAnalyticsEnabled] ?? 'true';
    return value.toLowerCase() == 'true';
  }

  /// التحقق من تفعيل Firebase Performance
  static bool isFirebasePerformanceEnabled() {
    _ensureInitialized();
    final value = _cachedConfig![_keyFirebasePerformanceEnabled] ?? 'true';
    return value.toLowerCase() == 'true';
  }

  /// التحقق من تفعيل Firebase Crashlytics
  static bool isFirebaseCrashlyticsEnabled() {
    _ensureInitialized();
    final value = _cachedConfig![_keyFirebaseCrashlyticsEnabled] ?? 'true';
    return value.toLowerCase() == 'true';
  }

  /// التحقق من تفعيل وضع التشخيص لـ Firebase
  static bool isFirebaseDebugMode() {
    _ensureInitialized();
    final value = _cachedConfig![_keyFirebaseDebugMode] ?? 'false';
    return value.toLowerCase() == 'true';
  }

  // ========================================
  // دوال قراءة إعدادات مراقبة الأداء
  // ========================================

  /// التحقق من تفعيل مراقبة الأداء
  static bool isPerformanceMonitoringEnabled() {
    _ensureInitialized();
    final value = _cachedConfig![_keyPerformanceMonitoringEnabled] ?? 'true';
    return value.toLowerCase() == 'true';
  }

  /// التحقق من تتبع طلبات الشبكة
  static bool isPerformanceTraceNetworkRequestsEnabled() {
    _ensureInitialized();
    final value = _cachedConfig![_keyPerformanceTraceNetworkRequests] ?? 'true';
    return value.toLowerCase() == 'true';
  }

  /// التحقق من تتبع عرض الشاشات
  static bool isPerformanceTraceScreenRenderingEnabled() {
    _ensureInitialized();
    final value = _cachedConfig![_keyPerformanceTraceScreenRendering] ?? 'true';
    return value.toLowerCase() == 'true';
  }

  /// التحقق من تتبع تفاعلات المستخدم
  static bool isPerformanceTraceUserInteractionsEnabled() {
    _ensureInitialized();
    final value =
        _cachedConfig![_keyPerformanceTraceUserInteractions] ?? 'true';
    return value.toLowerCase() == 'true';
  }

  // ========================================
  // دوال قراءة إعدادات Analytics
  // ========================================

  /// التحقق من جمع خصائص المستخدم
  static bool isAnalyticsCollectUserPropertiesEnabled() {
    _ensureInitialized();
    final value = _cachedConfig![_keyAnalyticsCollectUserProperties] ?? 'true';
    return value.toLowerCase() == 'true';
  }

  /// التحقق من جمع مشاهدات الشاشات
  static bool isAnalyticsCollectScreenViewsEnabled() {
    _ensureInitialized();
    final value = _cachedConfig![_keyAnalyticsCollectScreenViews] ?? 'true';
    return value.toLowerCase() == 'true';
  }

  /// التحقق من جمع الأحداث المخصصة
  static bool isAnalyticsCollectCustomEventsEnabled() {
    _ensureInitialized();
    final value = _cachedConfig![_keyAnalyticsCollectCustomEvents] ?? 'true';
    return value.toLowerCase() == 'true';
  }

  /// الحصول على مدة انتهاء جلسة Analytics (بالثواني)
  static int getAnalyticsSessionTimeoutDuration() {
    _ensureInitialized();
    final value = _cachedConfig![_keyAnalyticsSessionTimeoutDuration] ?? '1800';
    return int.tryParse(value) ?? 1800; // 30 دقيقة افتراضياً
  }

  /// التأكد من تهيئة الخدمة
  static void _ensureInitialized() {
    if (!_isInitialized) {
      throw Exception(
        'خدمة البيئة غير مهيأة. يرجى استدعاء EnvironmentService.initialize() أولاً',
      );
    }
  }
}
