import 'package:flutter/material.dart';
import '../services/language_service.dart';

/// مزود إدارة اللغة
class LanguageProvider extends ChangeNotifier {
  Locale _locale = const Locale('ar'); // العربية افتراضياً

  /// الحصول على اللغة الحالية
  Locale get locale => _locale;

  /// الحصول على كود اللغة الحالية
  String get currentLanguageCode => _locale.languageCode;

  /// تهيئة اللغة المحفوظة
  Future<void> initialize() async {
    try {
      final savedLanguage = await LanguageService.getSavedLanguage();
      _locale = LanguageService.getLocaleFromCode(savedLanguage);
      notifyListeners();
    } catch (e) {
      // في حالة الخطأ، نستخدم العربية افتراضياً
      _locale = const Locale('ar');
      notifyListeners();
    }
  }

  /// تغيير اللغة
  Future<bool> changeLanguage(String languageCode) async {
    try {
      // التحقق من دعم اللغة
      if (!LanguageService.isLanguageSupported(languageCode)) {
        return false;
      }

      // حفظ اللغة
      final success = await LanguageService.saveLanguage(languageCode);
      if (success) {
        _locale = LanguageService.getLocaleFromCode(languageCode);
        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  /// الحصول على اتجاه النص
  TextDirection get textDirection {
    return LanguageService.getTextDirection(currentLanguageCode);
  }

  /// الحصول على اسم اللغة الحالية
  String get currentLanguageName {
    return LanguageService.getLanguageNativeName(currentLanguageCode);
  }

  /// الحصول على علم اللغة الحالية
  String get currentLanguageFlag {
    return LanguageService.getLanguageFlag(currentLanguageCode);
  }

  /// التحقق من كون اللغة الحالية RTL
  bool get isRTL {
    final language = LanguageService.getLanguageFromCode(currentLanguageCode);
    return language?.isRTL ?? false;
  }
}
