import 'dart:io';
import 'package:crypto/crypto.dart';
import 'package:flutter/foundation.dart';
import 'package:http_certificate_pinning/http_certificate_pinning.dart';
import 'environment_service.dart';

/// خدمة Certificate Pinning لحماية الاتصالات من هجمات Man-in-the-Middle
/// تضمن أن التطبيق يتصل فقط بالخوادم الموثوقة باستخدام الشهادات المحددة مسبقاً
class CertificatePinningService {
  static CertificatePinningService? _instance;
  static CertificatePinningService get instance {
    _instance ??= CertificatePinningService._internal();
    return _instance!;
  }

  CertificatePinningService._internal();

  /// قائمة الشهادات المثبتة للخوادم الموثوقة
  static final List<String> _pinnedCertificates = [];

  /// حالة تهيئة الخدمة
  static bool _isInitialized = false;

  /// تهيئة خدمة Certificate Pinning
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // الحصول على عنوان الخادم من الإعدادات
      final serverUrl = EnvironmentService.getServerUrl();

      // الحصول على الـ fingerprint من إعدادات البيئة
      final envFingerprint = EnvironmentService.getCertificatePinningSha256();

      if (serverUrl.isNotEmpty && serverUrl != 'DUMMY_SERVER_URL') {
        // إضافة الـ fingerprint من إعدادات البيئة إذا كان متوفراً
        if (envFingerprint.isNotEmpty) {
          addPinnedCertificate(envFingerprint);
        } else {
          // إذا لم يكن هناك fingerprint في البيئة، احصل عليه من الخادم
          final uri = Uri.parse(serverUrl);
          final hostname = uri.host;
          await _addPinnedCertificatesForHost(hostname);
        }
      }

      _isInitialized = true;
    } catch (e) {
      // في حالة الخطأ، نستمر بدون Certificate Pinning
      _isInitialized = true;
    }
  }

  /// إضافة الشهادات المثبتة لخادم معين
  static Future<void> _addPinnedCertificatesForHost(String hostname) async {
    try {
      // الحصول على الشهادة من الخادم
      final certificate = await _getCertificateFromServer(hostname);

      if (certificate != null && certificate.isNotEmpty) {
        _pinnedCertificates.add(certificate);
      }
    } catch (e) {}
  }

  /// الحصول على الشهادة من الخادم
  static Future<String?> _getCertificateFromServer(String hostname) async {
    try {
      // محاولة الاتصال بالخادم للحصول على الشهادة
      final socket = await SecureSocket.connect(
        hostname,
        443, // HTTPS port
        timeout: const Duration(seconds: 10),
      );

      final certificate = socket.peerCertificate;
      await socket.close();

      if (certificate != null) {
        // تحويل الشهادة إلى SHA-256 fingerprint
        return _getCertificateFingerprint(certificate);
      }
    } catch (e) {}

    return null;
  }

  /// الحصول على بصمة الشهادة (SHA-256)
  static String _getCertificateFingerprint(X509Certificate certificate) {
    // تحويل الشهادة إلى بصمة SHA-256 بالتنسيق الصحيح
    final bytes = certificate.der;
    final digest = sha256.convert(bytes);
    final fingerprint = digest.bytes
        .map((byte) => byte.toRadixString(16).padLeft(2, '0').toUpperCase())
        .join(':');
    return fingerprint;
  }

  /// التحقق من صحة الاتصال باستخدام Certificate Pinning
  static Future<bool> checkCertificatePinning(String url) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      // إذا لم تكن هناك شهادات مثبتة، نسمح بالاتصال (للتطوير)
      if (_pinnedCertificates.isEmpty) {
        return true;
      }

      // التحقق من Certificate Pinning
      final result = await HttpCertificatePinning.check(
        serverURL: url,
        headerHttp: const {},
        sha: SHA.SHA256,
        allowedSHAFingerprints: _pinnedCertificates,
        timeout: 10,
      );

      if (result.contains('CONNECTION_SECURE')) {
        return true;
      } else {
        return false;
      }
    } catch (e) {
      // في حالة الخطأ، نرفض الاتصال للأمان
      return false;
    }
  }

  /// إضافة شهادة جديدة يدوياً
  static void addPinnedCertificate(String sha256Fingerprint) {
    // تنظيف الـ fingerprint من المسافات والأحرف الإضافية
    final cleanFingerprint = sha256Fingerprint
        .replaceAll(' ', '')
        .replaceAll('\n', '')
        .replaceAll('\r', '')
        .trim()
        .toUpperCase();

    if (!_pinnedCertificates.contains(cleanFingerprint)) {
      _pinnedCertificates.add(cleanFingerprint);
    }
  }

  /// إزالة جميع الشهادات المثبتة
  static void clearPinnedCertificates() {
    _pinnedCertificates.clear();
  }

  /// الحصول على عدد الشهادات المثبتة
  static int getPinnedCertificatesCount() {
    return _pinnedCertificates.length;
  }

  /// التحقق من حالة التهيئة
  static bool get isInitialized => _isInitialized;

  /// الحصول على قائمة الشهادات المثبتة (للتشخيص فقط)
  static List<String> getPinnedCertificates() {
    if (kDebugMode) {
      return List.from(_pinnedCertificates);
    }
    return [];
  }

  /// إعادة تعيين الخدمة
  static void reset() {
    _pinnedCertificates.clear();
    _isInitialized = false;
    _instance = null;
  }
}
