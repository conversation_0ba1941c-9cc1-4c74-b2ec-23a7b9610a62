# 🔥 دليل إعداد Firebase الحقيقي للإشعارات

## ✅ **ما تم إنجازه:**

### **1. الكود جاهز ومُحسَّن:**
- ✅ إصلاح إرسال FCM Token من التطبيق إلى Odoo
- ✅ تحسين API endpoints في Odoo لاستقبال FCM Token
- ✅ تحسين Firebase Admin SDK في Odoo مع معالجة أفضل للأخطاء
- ✅ إضافة دالة اختبار الإشعارات في التطبيق
- ✅ إضافة زر اختبار في شاشة الإعدادات

### **2. الميزات المضافة:**
- 🧪 **اختبار الإشعارات** - زر في الإعدادات لإرسال إشعار تجريبي
- 🔄 **إرسال FCM Token تلقائياً** - عند تسجيل الدخول
- 🛡️ **معالجة أخطاء متقدمة** - للـ FCM tokens غير الصالحة
- 📊 **تتبع Firebase Analytics** - لجميع عمليات الإشعارات

---

## 🚀 **المطلوب لجعل الإشعارات تعمل:**

### **الخطوة 1: إنشاء مشروع Firebase حقيقي**

1. **اذهب إلى Firebase Console:**
   ```
   https://console.firebase.google.com/
   ```

2. **إنشاء مشروع جديد:**
   - اضغط "Add project"
   - اسم المشروع: `odoo-employee-app-production` (أو أي اسم تريده)
   - فعّل Google Analytics (اختياري)

3. **إضافة تطبيق Android:**
   - اضغط على أيقونة Android
   - Package name: `com.example.odoo_employee_app` (نفس الاسم في `android/app/build.gradle`)
   - App nickname: `Odoo Employee App`
   - SHA-1: (اختياري للتطوير)

4. **إضافة تطبيق iOS (إذا كنت تريد دعم iOS):**
   - اضغط على أيقونة iOS
   - Bundle ID: `com.example.odooEmployeeApp`
   - App nickname: `Odoo Employee App iOS`

### **الخطوة 2: تحديث ملفات التكوين**

#### **A) Android - استبدال google-services.json:**
```bash
# 1. حمّل google-services.json الجديد من Firebase Console
# 2. استبدل الملف الحالي:
cp /path/to/new/google-services.json android/app/google-services.json
```

#### **B) iOS - إضافة GoogleService-Info.plist:**
```bash
# 1. حمّل GoogleService-Info.plist من Firebase Console
# 2. أضفه إلى مجلد iOS:
cp /path/to/GoogleService-Info.plist ios/Runner/GoogleService-Info.plist
```

### **الخطوة 3: إعداد Firebase Admin SDK في Odoo**

#### **A) إنشاء Service Account:**
1. في Firebase Console، اذهب إلى:
   ```
   Project Settings > Service accounts > Generate new private key
   ```

2. حمّل ملف JSON وضعه في خادم Odoo:
   ```bash
   # في خادم Odoo
   mkdir -p /opt/odoo/firebase
   cp firebase-service-account.json /opt/odoo/firebase/
   chmod 600 /opt/odoo/firebase/firebase-service-account.json
   ```

#### **B) تعيين متغير البيئة:**
```bash
# في ملف .env أو في إعدادات النظام
export FIREBASE_SERVICE_ACCOUNT_PATH="/opt/odoo/firebase/firebase-service-account.json"
```

#### **C) إعادة تشغيل Odoo:**
```bash
sudo systemctl restart odoo
```

### **الخطوة 4: اختبار النظام**

#### **A) اختبار من التطبيق:**
1. سجل الدخول في التطبيق
2. اذهب إلى الإعدادات
3. اضغط على "اختبار الإشعارات"
4. يجب أن تصل رسالة إشعار

#### **B) اختبار من Odoo:**
```python
# في Odoo shell
user = env['res.users'].browse(USER_ID)
user.test_fcm_notification(USER_ID)
```

### **الخطوة 5: مراقبة الأخطاء**

#### **A) في Odoo logs:**
```bash
tail -f /var/log/odoo/odoo.log | grep FCM
```

#### **B) في Firebase Console:**
- اذهب إلى Cloud Messaging > Reports
- راقب معدلات التسليم والأخطاء

---

## 🔧 **استكشاف الأخطاء:**

### **مشكلة: "No FCM token for user"**
**الحل:**
1. تأكد من تسجيل الدخول في التطبيق
2. تحقق من صلاحيات الإشعارات في الجهاز
3. أعد تشغيل التطبيق

### **مشكلة: "Firebase Admin SDK not initialized"**
**الحل:**
1. تأكد من وجود ملف service account
2. تحقق من متغير البيئة `FIREBASE_SERVICE_ACCOUNT_PATH`
3. أعد تشغيل Odoo

### **مشكلة: "FCM token is invalid"**
**الحل:**
1. تأكد من تطابق package name في Firebase مع التطبيق
2. تحقق من صحة ملف google-services.json
3. أعد بناء التطبيق

### **مشكلة: "Permission denied"**
**الحل:**
1. تحقق من صلاحيات ملف service account
2. تأكد من أن Odoo يمكنه قراءة الملف
3. تحقق من صلاحيات المستخدم في Odoo

---

## 📊 **مراقبة الأداء:**

### **Firebase Analytics Events:**
- `fcm_token_sent` - نجاح إرسال Token
- `fcm_token_send_failed` - فشل إرسال Token
- `test_notification_sent` - نجاح إرسال إشعار تجريبي
- `test_notification_failed` - فشل إرسال إشعار تجريبي
- `notification_received` - استقبال إشعار

### **Odoo Logs:**
```
INFO: FCM Token saved for user [username]: [token]...
INFO: Firebase Admin FCM notification sent successfully to [user]: [response]
ERROR: Error sending Firebase Admin FCM notification: [error]
```

---

## 🎯 **الخطوات التالية:**

1. **إنشاء مشروع Firebase حقيقي** ✋ **← ابدأ من هنا**
2. **استبدال ملفات التكوين**
3. **إعداد Service Account**
4. **اختبار النظام**
5. **مراقبة الأداء**

**ملاحظة:** النظام الحالي يعمل بملفات تجريبية، لكن للإنتاج يجب استبدالها بملفات حقيقية من Firebase Console.
