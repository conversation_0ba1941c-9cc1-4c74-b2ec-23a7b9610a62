import 'package:shared_preferences/shared_preferences.dart';
import 'secure_storage_service.dart';

/// خدمة إدارة التخزين المحلي لبيانات المستخدم
/// تستخدم التشفير الآمن لحماية البيانات الحساسة
class StorageService {
  static const String _keyEmail = 'user_email';
  static const String _keyPassword = 'user_password';
  static const String _keyRememberMe = 'remember_me';
  static const String _keyServerUrl = 'server_url';
  static const String _keyDatabase = 'database';
  static const String _keyBiometricEnabled = 'biometric_enabled';

  // مفاتيح منفصلة لبيانات البصمة
  static const String _keyBiometricEmail = 'biometric_email';
  static const String _keyBiometricPassword = 'biometric_password';
  static const String _keyBiometricServerUrl = 'biometric_server_url';
  static const String _keyBiometricDatabase = 'biometric_database';

  /// حفظ بيانات تسجيل الدخول مع التشفير الآمن
  static Future<void> saveLoginCredentials({
    required String email,
    required String password,
    required bool rememberMe,
    String? serverUrl,
    String? database,
  }) async {
    try {
      // تهيئة خدمة التشفير
      await SecureStorageService.initialize();

      final prefs = await SharedPreferences.getInstance();

      if (rememberMe) {
        // حفظ البريد الإلكتروني بدون تشفير (ليس حساساً جداً)
        await prefs.setString(_keyEmail, email);

        // حفظ كلمة المرور مشفرة (البيانات الحساسة)
        await SecureStorageService.setSecureString(_keyPassword, password);

        // حفظ معلومات الخادم بدون تشفير (معلومات عامة)
        if (serverUrl != null) {
          await prefs.setString(_keyServerUrl, serverUrl);
        }
        if (database != null) {
          await prefs.setString(_keyDatabase, database);
        }
      } else {
        // إذا لم يختر المستخدم "تذكرني"، احذف البيانات المحفوظة
        await clearLoginCredentials();
      }

      await prefs.setBool(_keyRememberMe, rememberMe);
    } catch (e) {
      throw Exception('فشل في حفظ بيانات تسجيل الدخول: $e');
    }
  }

  /// استرجاع بيانات تسجيل الدخول المحفوظة مع فك التشفير
  static Future<Map<String, String?>> getLoginCredentials() async {
    try {
      // تهيئة خدمة التشفير
      await SecureStorageService.initialize();

      final prefs = await SharedPreferences.getInstance();
      final rememberMe = prefs.getBool(_keyRememberMe) ?? false;

      if (!rememberMe) {
        return {
          'email': null,
          'password': null,
          'serverUrl': null,
          'database': null,
          'rememberMe': 'false',
        };
      }

      // استرجاع كلمة المرور المشفرة
      final encryptedPassword = await SecureStorageService.getSecureString(
        _keyPassword,
      );

      return {
        'email': prefs.getString(_keyEmail),
        'password': encryptedPassword, // كلمة المرور مفكوكة التشفير
        'serverUrl': prefs.getString(_keyServerUrl),
        'database': prefs.getString(_keyDatabase),
        'rememberMe': rememberMe.toString(),
      };
    } catch (e) {
      // في حالة فشل فك التشفير، نرجع بيانات فارغة
      return {
        'email': null,
        'password': null,
        'serverUrl': null,
        'database': null,
        'rememberMe': 'false',
      };
    }
  }

  /// التحقق من وجود بيانات محفوظة مع التحقق من التشفير
  static Future<bool> hasRememberedCredentials() async {
    try {
      // تهيئة خدمة التشفير
      await SecureStorageService.initialize();

      final prefs = await SharedPreferences.getInstance();
      final rememberMe = prefs.getBool(_keyRememberMe) ?? false;
      final email = prefs.getString(_keyEmail);

      // التحقق من وجود كلمة المرور المشفرة
      final hasEncryptedPassword = await SecureStorageService.hasSecureString(
        _keyPassword,
      );

      return rememberMe && email != null && hasEncryptedPassword;
    } catch (e) {
      return false;
    }
  }

  /// مسح بيانات تسجيل الدخول المحفوظة مع البيانات المشفرة
  static Future<void> clearLoginCredentials() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // مسح البيانات العادية
      await prefs.remove(_keyEmail);
      await prefs.remove(_keyServerUrl);
      await prefs.remove(_keyDatabase);
      await prefs.setBool(_keyRememberMe, false);

      // مسح كلمة المرور المشفرة
      await SecureStorageService.removeSecureString(_keyPassword);
    } catch (e) {
      // في حالة فشل المسح، نحاول مسح البيانات العادية على الأقل
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_keyEmail);
      await prefs.remove(_keyPassword); // البيانات القديمة غير المشفرة
      await prefs.remove(_keyServerUrl);
      await prefs.remove(_keyDatabase);
      await prefs.setBool(_keyRememberMe, false);
    }
  }

  /// حفظ إعداد "تذكرني" فقط
  static Future<void> setRememberMe(bool rememberMe) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_keyRememberMe, rememberMe);
  }

  /// الحصول على حالة "تذكرني"
  static Future<bool> getRememberMe() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_keyRememberMe) ?? false;
  }

  /// حفظ البريد الإلكتروني فقط (للاستخدام مع خيار تذكر البريد الإلكتروني فقط)
  static Future<void> saveEmailOnly(String email) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_keyEmail, email);
  }

  /// الحصول على البريد الإلكتروني المحفوظ
  static Future<String?> getSavedEmail() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_keyEmail);
  }

  /// مسح جميع البيانات المحفوظة مع مفاتيح التشفير
  static Future<void> clearAllData() async {
    try {
      // مسح جميع البيانات المشفرة ومفاتيح التشفير
      await SecureStorageService.clearAllEncryptionKeys();

      // مسح جميع البيانات العادية
      final prefs = await SharedPreferences.getInstance();
      await prefs.clear();
    } catch (e) {
      // في حالة فشل المسح الآمن، نمسح البيانات العادية على الأقل
      final prefs = await SharedPreferences.getInstance();
      await prefs.clear();
    }
  }

  /// حفظ إعدادات الخادم
  static Future<void> saveServerSettings({
    required String serverUrl,
    required String database,
  }) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_keyServerUrl, serverUrl);
    await prefs.setString(_keyDatabase, database);
  }

  /// الحصول على إعدادات الخادم المحفوظة
  static Future<Map<String, String?>> getServerSettings() async {
    final prefs = await SharedPreferences.getInstance();
    return {
      'serverUrl': prefs.getString(_keyServerUrl),
      'database': prefs.getString(_keyDatabase),
    };
  }

  /// تفعيل/إلغاء تفعيل المصادقة البيومترية
  static Future<void> setBiometricEnabled(bool enabled) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_keyBiometricEnabled, enabled);
  }

  /// التحقق من تفعيل المصادقة البيومترية
  static Future<bool> isBiometricEnabled() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_keyBiometricEnabled) ?? false;
  }

  /// مسح إعدادات البصمة
  static Future<void> clearBiometricSettings() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_keyBiometricEnabled);
  }

  /// ترقية البيانات القديمة غير المشفرة إلى النظام الجديد المشفر
  /// يستخدم عند التحديث من إصدار قديم
  static Future<bool> migrateOldCredentials() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // التحقق من وجود كلمة مرور قديمة غير مشفرة
      final oldPassword = prefs.getString(_keyPassword);
      if (oldPassword == null) {
        return false; // لا توجد بيانات قديمة
      }

      // التحقق من عدم وجود كلمة مرور مشفرة بالفعل
      final hasEncryptedPassword = await SecureStorageService.hasSecureString(
        _keyPassword,
      );
      if (hasEncryptedPassword) {
        return false; // البيانات مشفرة بالفعل
      }

      // تشفير كلمة المرور القديمة
      await SecureStorageService.setSecureString(_keyPassword, oldPassword);

      // حذف كلمة المرور القديمة غير المشفرة
      await prefs.remove(_keyPassword);

      return true; // تمت الترقية بنجاح
    } catch (e) {
      return false; // فشلت الترقية
    }
  }

  // ==================== إدارة بيانات البصمة المنفصلة ====================

  /// حفظ بيانات تسجيل الدخول للبصمة (منفصلة عن تذكر البيانات)
  static Future<void> saveBiometricCredentials({
    required String email,
    required String password,
    String? serverUrl,
    String? database,
  }) async {
    try {
      // تهيئة خدمة التشفير
      await SecureStorageService.initialize();

      final prefs = await SharedPreferences.getInstance();

      // حفظ البيانات مع التشفير
      await prefs.setString(_keyBiometricEmail, email);
      await SecureStorageService.setSecureString(
        _keyBiometricPassword,
        password,
      );

      if (serverUrl != null) {
        await prefs.setString(_keyBiometricServerUrl, serverUrl);
      }
      if (database != null) {
        await prefs.setString(_keyBiometricDatabase, database);
      }
    } catch (e) {
      throw Exception('فشل في حفظ بيانات البصمة: $e');
    }
  }

  /// استرجاع بيانات تسجيل الدخول المحفوظة للبصمة
  static Future<Map<String, String?>> getBiometricCredentials() async {
    try {
      // تهيئة خدمة التشفير
      await SecureStorageService.initialize();

      final prefs = await SharedPreferences.getInstance();

      // استرجاع البيانات مع فك التشفير
      final email = prefs.getString(_keyBiometricEmail);
      final password = await SecureStorageService.getSecureString(
        _keyBiometricPassword,
      );
      final serverUrl = prefs.getString(_keyBiometricServerUrl);
      final database = prefs.getString(_keyBiometricDatabase);

      return {
        'email': email,
        'password': password,
        'serverUrl': serverUrl,
        'database': database,
      };
    } catch (e) {
      return {
        'email': null,
        'password': null,
        'serverUrl': null,
        'database': null,
      };
    }
  }

  /// التحقق من وجود بيانات البصمة المحفوظة
  static Future<bool> hasBiometricCredentials() async {
    try {
      final credentials = await getBiometricCredentials();
      return credentials['email'] != null && credentials['password'] != null;
    } catch (e) {
      return false;
    }
  }

  /// مسح بيانات البصمة المحفوظة
  static Future<void> clearBiometricCredentials() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // مسح جميع بيانات البصمة
      await prefs.remove(_keyBiometricEmail);
      await SecureStorageService.removeSecureString(_keyBiometricPassword);
      await prefs.remove(_keyBiometricServerUrl);
      await prefs.remove(_keyBiometricDatabase);
    } catch (e) {
      throw Exception('فشل في مسح بيانات البصمة: $e');
    }
  }
}
