import 'package:flutter/material.dart';
import '../services/session_manager_service.dart';
import '../config/app_config.dart';

/// مربع حوار تنبيه انتهاء الجلسة
/// يظهر للمستخدم عندما تقترب الجلسة من الانتهاء
class SessionTimeoutDialog extends StatefulWidget {
  /// دالة callback عند اختيار تمديد الجلسة
  final VoidCallback? onExtendSession;

  /// دالة callback عند اختيار تسجيل الخروج
  final VoidCallback? onLogout;

  const SessionTimeoutDialog({super.key, this.onExtendSession, this.onLogout});

  @override
  State<SessionTimeoutDialog> createState() => _SessionTimeoutDialogState();
}

class _SessionTimeoutDialogState extends State<SessionTimeoutDialog> {
  int _remainingMinutes = 0;

  @override
  void initState() {
    super.initState();
    _updateRemainingTime();

    // تحديث الوقت المتبقي كل ثانية
    _startCountdown();
  }

  void _updateRemainingTime() {
    if (mounted) {
      setState(() {
        _remainingMinutes = SessionManagerService.instance
            .getRemainingMinutes();
      });
    }
  }

  void _startCountdown() {
    Future.delayed(const Duration(seconds: 1), () {
      if (mounted) {
        _updateRemainingTime();

        // إذا انتهت الجلسة، أغلق الحوار وسجل خروج
        if (_remainingMinutes <= 0) {
          Navigator.of(context).pop();
          widget.onLogout?.call();
        } else {
          _startCountdown();
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      title: Row(
        children: [
          const Icon(Icons.access_time, color: Colors.orange, size: 28),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              'تنبيه انتهاء الجلسة',
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.orange,
              ),
            ),
          ),
        ],
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'ستنتهي جلستك قريباً بسبب عدم النشاط.',
            style: TextStyle(fontSize: 16, height: 1.5),
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.orange.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: Colors.orange.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                const Icon(Icons.timer, color: Colors.orange, size: 20),
                const SizedBox(width: 8),
                const Text(
                  'الوقت المتبقي:',
                  style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
                ),
                const SizedBox(width: 8),
                Text(
                  '$_remainingMinutes دقيقة',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.orange,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'يمكنك تمديد الجلسة أو تسجيل الخروج الآن.',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
              height: 1.4,
            ),
          ),
        ],
      ),
      actions: [
        // زر تسجيل الخروج
        TextButton(
          onPressed: () {
            Navigator.of(context).pop();
            widget.onLogout?.call();
          },
          style: TextButton.styleFrom(
            foregroundColor: Colors.red,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.logout, size: 18),
              const SizedBox(width: 4),
              const Text('تسجيل الخروج', style: TextStyle(fontSize: 14)),
            ],
          ),
        ),

        // زر تمديد الجلسة
        ElevatedButton(
          onPressed: () {
            Navigator.of(context).pop();
            widget.onExtendSession?.call();
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: Color(AppConfig.primaryColor),
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.refresh, size: 18),
              const SizedBox(width: 4),
              const Text(
                'تمديد الجلسة',
                style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

/// مكون تنبيه انتهاء الجلسة المبسط
/// يظهر كـ SnackBar في أسفل الشاشة
class SessionTimeoutSnackBar {
  static void show(
    BuildContext context, {
    VoidCallback? onExtendSession,
    VoidCallback? onLogout,
  }) {
    final remainingMinutes = SessionManagerService.instance
        .getRemainingMinutes();

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(Icons.access_time, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                'ستنتهي الجلسة خلال $remainingMinutes دقيقة',
                style: const TextStyle(fontSize: 14, color: Colors.white),
              ),
            ),
          ],
        ),
        backgroundColor: Colors.orange,
        duration: const Duration(seconds: 10),
        action: SnackBarAction(
          label: 'تمديد',
          textColor: Colors.white,
          onPressed: () {
            onExtendSession?.call();
          },
        ),
      ),
    );
  }
}

/// مكون إدارة تنبيهات الجلسة
/// يدير عرض التنبيهات بناءً على إعدادات التطبيق
class SessionTimeoutManager {
  static void showWarning(
    BuildContext context, {
    VoidCallback? onExtendSession,
    VoidCallback? onLogout,
    bool useDialog = true,
  }) {
    if (!AppConfig.enableSessionWarnings) return;

    if (useDialog) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => SessionTimeoutDialog(
          onExtendSession: onExtendSession,
          onLogout: onLogout,
        ),
      );
    } else {
      SessionTimeoutSnackBar.show(
        context,
        onExtendSession: onExtendSession,
        onLogout: onLogout,
      );
    }
  }
}
