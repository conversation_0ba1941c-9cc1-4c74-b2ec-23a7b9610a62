from odoo import models, fields, api
import requests
import json
import logging

_logger = logging.getLogger(__name__)

try:
    import firebase_admin
    from firebase_admin import credentials, messaging
    FIREBASE_AVAILABLE = True
except ImportError:
    FIREBASE_AVAILABLE = False
    _logger.warning('Firebase Admin SDK not available. Install with: pip install firebase-admin')

class ResUsers(models.Model):
    _inherit = 'res.users'

    fcm_token = fields.Char(string='FCM Token', help='Firebase Cloud Messaging Token for mobile notifications')
    device_platform = fields.Selection([
        ('android', 'Android'),
        ('ios', 'iOS'),
        ('web', 'Web')
    ], string='Device Platform')

    @api.model
    def save_fcm_token(self, user_id, fcm_token, platform='android'):
        """Save FCM token for user - called from mobile app"""
        try:
            # التحقق من صحة المعاملات
            if not user_id or not fcm_token:
                _logger.warning('Invalid parameters: user_id or fcm_token is empty')
                return {'success': False, 'message': 'Invalid parameters: user_id and fcm_token are required'}

            # التحقق من صحة platform
            valid_platforms = ['android', 'ios', 'web']
            if platform not in valid_platforms:
                platform = 'android'  # قيمة افتراضية
                _logger.warning(f'Invalid platform provided, using default: android')

            user = self.browse(user_id)
            if user.exists():
                # التحقق من الصلاحيات
                if not user.has_group('base.group_user'):
                    _logger.warning(f'User {user.name} does not have required permissions')
                    return {'success': False, 'message': 'Insufficient permissions'}

                # حفظ Token
                old_token = user.fcm_token
                user.write({
                    'fcm_token': fcm_token,
                    'device_platform': platform
                })

                # تسجيل العملية
                if old_token != fcm_token:
                    _logger.info(f'FCM Token updated for user {user.name} ({user.login}): {fcm_token[:20]}... (Platform: {platform})')
                else:
                    _logger.info(f'FCM Token refreshed for user {user.name} ({user.login}): {fcm_token[:20]}... (Platform: {platform})')

                return {
                    'success': True,
                    'message': 'FCM Token saved successfully',
                    'user_name': user.name,
                    'platform': platform
                }
            else:
                _logger.error(f'User with ID {user_id} not found')
                return {'success': False, 'message': f'User with ID {user_id} not found'}

        except Exception as e:
            _logger.error(f'Error saving FCM token for user {user_id}: {str(e)}')
            return {'success': False, 'message': f'Server error: {str(e)}'}

    @api.model
    def test_fcm_notification(self, user_id):
        """Send test FCM notification - for testing purposes"""
        try:
            user = self.browse(user_id)
            if not user.exists():
                return {'success': False, 'message': 'User not found'}

            if not user.fcm_token:
                return {'success': False, 'message': 'No FCM token found for user'}

            # إرسال إشعار تجريبي
            title = "اختبار الإشعارات"
            body = f"مرحباً {user.name}! هذا إشعار تجريبي للتأكد من عمل النظام."
            data = {
                'type': 'test_notification',
                'timestamp': str(fields.Datetime.now()),
                'user_id': str(user_id)
            }

            success = user.send_fcm_notification(title, body, data)

            if success:
                _logger.info(f'Test notification sent successfully to user {user.name}')
                return {'success': True, 'message': 'Test notification sent successfully'}
            else:
                _logger.error(f'Failed to send test notification to user {user.name}')
                return {'success': False, 'message': 'Failed to send test notification'}

        except Exception as e:
            _logger.error(f'Error sending test notification: {str(e)}')
            return {'success': False, 'message': str(e)}

    def send_fcm_notification(self, title, body, data=None):
        """Send FCM notification using Firebase Admin SDK"""
        if not self.fcm_token:
            _logger.warning(f'No FCM token for user {self.name}')
            return False

        if FIREBASE_AVAILABLE:
            # استخدام Firebase Admin SDK
            success = self._send_firebase_admin_fcm(title, body, data)
        else:
            # استخدام الطريقة البديلة
            success = self._send_alternative_fcm(title, body, data)

        return success

    def _send_firebase_admin_fcm(self, title, body, data):
        """Send using Firebase Admin SDK"""
        try:
            # التحقق من وجود FCM Token
            if not self.fcm_token:
                _logger.warning(f'No FCM token for user {self.name}')
                return False

            # تهيئة Firebase Admin إذا لم يتم تهيئته
            if not firebase_admin._apps:
                # محاولة الحصول على إعدادات Firebase من متغيرات البيئة أولاً
                import os
                firebase_config_path = os.environ.get('FIREBASE_SERVICE_ACCOUNT_PATH')

                if firebase_config_path and os.path.exists(firebase_config_path):
                    # استخدام ملف service account من متغير البيئة
                    cred = credentials.Certificate(firebase_config_path)
                    _logger.info('Using Firebase service account from environment variable')
                else:
                    # استخدام الإعدادات المدمجة (للاختبار فقط)
                    _logger.warning('Using embedded Firebase credentials - NOT RECOMMENDED for production')
                    service_account_info = **************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
                    cred = credentials.Certificate(service_account_info)

                firebase_admin.initialize_app(cred)
                _logger.info('Firebase Admin SDK initialized successfully')

            # إنشاء رسالة الإشعار مع معالجة أفضل للبيانات
            notification_data = {}
            if data:
                # تحويل جميع القيم إلى strings (مطلوب لـ FCM)
                for key, value in data.items():
                    notification_data[key] = str(value) if value is not None else ''

            message = messaging.Message(
                notification=messaging.Notification(
                    title=title,
                    body=body
                ),
                data=notification_data,
                token=self.fcm_token,
                android=messaging.AndroidConfig(
                    notification=messaging.AndroidNotification(
                        sound='default',
                        click_action='FLUTTER_NOTIFICATION_CLICK',
                        channel_id='default'
                    ),
                    priority='high'
                ),
                apns=messaging.APNSConfig(
                    payload=messaging.APNSPayload(
                        aps=messaging.Aps(
                            sound='default',
                            badge=1
                        )
                    )
                )
            )

            # إرسال الإشعار مع معالجة الأخطاء
            response = messaging.send(message)
            _logger.info(f'Firebase Admin FCM notification sent successfully to {self.name} ({self.login}): {response}')
            return True

        except messaging.UnregisteredError:
            _logger.warning(f'FCM token is invalid or unregistered for user {self.name}. Clearing token.')
            # مسح Token غير الصالح
            self.write({'fcm_token': False})
            return False
        except messaging.SenderIdMismatchError:
            _logger.error(f'FCM Sender ID mismatch for user {self.name}')
            return False
        except Exception as e:
            _logger.error(f'Error sending Firebase Admin FCM notification to {self.name}: {str(e)}')
            return False

    def _send_alternative_fcm(self, title, body, data):
        """Alternative FCM method - log notification for manual testing"""
        try:
            # للاختبار: سجل الإشعار في log
            _logger.info(f'=== FCM NOTIFICATION ===')
            _logger.info(f'To: {self.name} ({self.fcm_token[:20]}...)')
            _logger.info(f'Title: {title}')
            _logger.info(f'Body: {body}')
            _logger.info(f'Data: {data}')
            _logger.info(f'========================')

            # يمكن إضافة طرق بديلة هنا مثل:
            # - إرسال email
            # - حفظ في قاعدة البيانات
            # - استخدام خدمة إشعارات أخرى

            return True
        except Exception as e:
            _logger.error(f'Error in alternative FCM method: {str(e)}')
            return False
