from odoo import models, fields, api
import requests
import json
import logging

_logger = logging.getLogger(__name__)

try:
    import firebase_admin
    from firebase_admin import credentials, messaging
    FIREBASE_AVAILABLE = True
except ImportError:
    FIREBASE_AVAILABLE = False
    _logger.warning('Firebase Admin SDK not available. Install with: pip install firebase-admin')

class ResUsers(models.Model):
    _inherit = 'res.users'

    fcm_token = fields.Char(string='FCM Token', help='Firebase Cloud Messaging Token for mobile notifications')
    device_platform = fields.Selection([
        ('android', 'Android'),
        ('ios', 'iOS'),
        ('web', 'Web')
    ], string='Device Platform')

    @api.model
    def save_fcm_token(self, user_id, fcm_token, platform='android'):
        """Save FCM token for user - called from mobile app"""
        try:
            user = self.browse(user_id)
            if user.exists():
                user.write({
                    'fcm_token': fcm_token,
                    'device_platform': platform
                })
                _logger.info(f'FCM Token saved for user {user.name}: {fcm_token[:20]}...')
                return {'success': True, 'message': 'FCM Token saved successfully'}
            else:
                return {'success': False, 'message': 'User not found'}
        except Exception as e:
            _logger.error(f'Error saving FCM token: {str(e)}')
            return {'success': False, 'message': str(e)}

    def send_fcm_notification(self, title, body, data=None):
        """Send FCM notification using Firebase Admin SDK"""
        if not self.fcm_token:
            _logger.warning(f'No FCM token for user {self.name}')
            return False

        if FIREBASE_AVAILABLE:
            # استخدام Firebase Admin SDK
            success = self._send_firebase_admin_fcm(title, body, data)
        else:
            # استخدام الطريقة البديلة
            success = self._send_alternative_fcm(title, body, data)

        return success

    def _send_firebase_admin_fcm(self, title, body, data):
        """Send using Firebase Admin SDK"""
        try:
            # تهيئة Firebase Admin إذا لم يتم تهيئته
            if not firebase_admin._apps:
                service_account_info = **************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

                cred = credentials.Certificate(service_account_info)
                firebase_admin.initialize_app(cred)
                _logger.info('Firebase Admin SDK initialized successfully')

            # إنشاء رسالة الإشعار
            message = messaging.Message(
                notification=messaging.Notification(
                    title=title,
                    body=body
                ),
                data=data or {},
                token=self.fcm_token,
                android=messaging.AndroidConfig(
                    notification=messaging.AndroidNotification(
                        sound='default',
                        click_action='FLUTTER_NOTIFICATION_CLICK'
                    )
                )
            )

            # إرسال الإشعار
            response = messaging.send(message)
            _logger.info(f'Firebase Admin FCM notification sent successfully to {self.name}: {response}')
            return True

        except Exception as e:
            _logger.error(f'Error sending Firebase Admin FCM notification: {str(e)}')
            return False

    def _send_alternative_fcm(self, title, body, data):
        """Alternative FCM method - log notification for manual testing"""
        try:
            # للاختبار: سجل الإشعار في log
            _logger.info(f'=== FCM NOTIFICATION ===')
            _logger.info(f'To: {self.name} ({self.fcm_token[:20]}...)')
            _logger.info(f'Title: {title}')
            _logger.info(f'Body: {body}')
            _logger.info(f'Data: {data}')
            _logger.info(f'========================')

            # يمكن إضافة طرق بديلة هنا مثل:
            # - إرسال email
            # - حفظ في قاعدة البيانات
            # - استخدام خدمة إشعارات أخرى

            return True
        except Exception as e:
            _logger.error(f'Error in alternative FCM method: {str(e)}')
            return False
