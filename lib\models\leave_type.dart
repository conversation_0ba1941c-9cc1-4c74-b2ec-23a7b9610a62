/// نموذج بيانات نوع الإجازة
class LeaveType {
  final int id;
  final String name;
  final bool requiresAllocation; // هل يحتاج هذا النوع إلى تخصيص

  LeaveType({
    required this.id,
    required this.name,
    required this.requiresAllocation,
  });

  /// إنشاء كائن LeaveType من البيانات المستلمة من Odoo
  factory LeaveType.fromOdooData(Map<String, dynamic> data) {
    // تحويل requires_allocation من نص إلى قيمة منطقية
    bool requiresAllocation = false;
    final allocationValue = data['requires_allocation'];
    if (allocationValue is String) {
      requiresAllocation = allocationValue.toLowerCase() == 'yes';
    } else if (allocationValue is bool) {
      requiresAllocation = allocationValue;
    }

    return LeaveType(
      id: data['id'] as int,
      name: data['name'] as String,
      requiresAllocation: requiresAllocation,
    );
  }

  /// تحويل الكائن إلى Map
  Map<String, dynamic> toMap() {
    return {'id': id, 'name': name, 'requiresAllocation': requiresAllocation};
  }

  @override
  String toString() {
    return 'LeaveType{id: $id, name: $name, requiresAllocation: $requiresAllocation}';
  }
}
