# دليل إعداد الإشعارات - Firebase Cloud Messaging

## ✅ **تم تطبيقه في التطبيق:**

### **1. الإشعارات المدعومة:**
- ✅ **تم قبول طلب الإجازة** - للموظف
- ❌ **تم رفض طلب الإجازة** - للموظف مع السبب
- 📋 **طلب إجازة جديد** - للمدير

### **2. الملفات المضافة:**
- `lib/services/notification_service.dart` - خدمة الإشعارات
- تحديث `main.dart` - تهيئة الخدمة
- تحديث `login_screen.dart` - إرسال FCM Token

---

## 🔧 **المطلوب إعداده:**

### **1. في Firebase Console:**
```
1. اذهب إلى Firebase Console
2. اختر مشروعك
3. اذهب إلى Project Settings > Cloud Messaging
4. احفظ Server Key للاستخدام في Odoo
```

### **2. في خادم Odoo:**
يجب إنشاء API endpoints للإشعارات:

#### **A) حفظ FCM Token:**
```python
# في Odoo - إضافة حقل fcm_token لجدول res.users
@api.model
def save_fcm_token(self, user_id, fcm_token, platform):
    user = self.env['res.users'].browse(user_id)
    user.write({
        'fcm_token': fcm_token,
        'device_platform': platform
    })
    return True
```

#### **B) إرسال إشعار قبول الإجازة:**
```python
def approve_leave_request(self, leave_id):
    leave = self.env['hr.leave'].browse(leave_id)
    leave.action_approve()
    
    # إرسال إشعار
    self._send_fcm_notification(
        user_id=leave.employee_id.user_id.id,
        title="تم قبول طلب الإجازة",
        body=f"تم قبول طلبك للإجازة من {leave.request_date_from} إلى {leave.request_date_to}",
        data={
            'type': 'leave_approved',
            'leave_id': str(leave_id)
        }
    )
```

#### **C) إرسال إشعار رفض الإجازة:**
```python
def refuse_leave_request(self, leave_id, reason=""):
    leave = self.env['hr.leave'].browse(leave_id)
    leave.action_refuse()
    
    # إرسال إشعار
    self._send_fcm_notification(
        user_id=leave.employee_id.user_id.id,
        title="تم رفض طلب الإجازة",
        body=f"تم رفض طلبك للإجازة. السبب: {reason}",
        data={
            'type': 'leave_rejected',
            'leave_id': str(leave_id),
            'reason': reason
        }
    )
```

#### **D) إرسال إشعار طلب جديد للمدير:**
```python
def create_leave_request(self, vals):
    leave = super().create(vals)
    
    # إرسال إشعار للمدير
    manager = leave.employee_id.parent_id
    if manager and manager.user_id:
        self._send_fcm_notification(
            user_id=manager.user_id.id,
            title="طلب إجازة جديد",
            body=f"طلب إجازة جديد من {leave.employee_id.name}",
            data={
                'type': 'new_leave_request',
                'leave_id': str(leave.id),
                'employee_name': leave.employee_id.name
            }
        )
    
    return leave
```

#### **E) دالة إرسال FCM:**
```python
import requests
import json

def _send_fcm_notification(self, user_id, title, body, data=None):
    user = self.env['res.users'].browse(user_id)
    if not user.fcm_token:
        return False
    
    fcm_url = "https://fcm.googleapis.com/fcm/send"
    headers = {
        'Authorization': 'key=YOUR_SERVER_KEY_HERE',
        'Content-Type': 'application/json'
    }
    
    payload = {
        'to': user.fcm_token,
        'notification': {
            'title': title,
            'body': body,
            'sound': 'default'
        },
        'data': data or {}
    }
    
    response = requests.post(fcm_url, headers=headers, data=json.dumps(payload))
    return response.status_code == 200
```

---

## 📱 **للاختبار:**

### **1. تشغيل التطبيق:**
```bash
flutter pub get
flutter run
```

### **2. فحص FCM Token:**
- سيظهر في console عند تسجيل الدخول
- يتم إرساله تلقائياً للخادم

### **3. اختبار الإشعارات:**
- قم بتقديم طلب إجازة
- اطلب من المدير الموافقة/الرفض
- ستصل الإشعارات تلقائياً

---

## 🎯 **النتيجة:**
- إشعارات فورية عند قبول/رفض الإجازة
- إشعار للمدير عند طلب جديد
- تتبع كامل في Firebase Analytics
- يعمل حتى لو التطبيق مغلق

**الإشعارات جاهزة للعمل!** 🚀
