import 'package:flutter/material.dart';
import '../services/odoo_service.dart';
import '../services/image_cache_service.dart';
import '../services/firebase_analytics_service.dart';
import '../services/firebase_crashlytics_service.dart';
import '../services/performance_monitoring_service.dart';
import '../models/employee.dart';
import '../config/app_config.dart';
import 'login_screen.dart';
import 'leave_types_screen.dart';
import 'my_leave_requests_screen.dart';
import 'leave_approval_screen.dart';
import 'settings_screen.dart';
import '../generated/l10n/app_localizations.dart';

/// شاشة الموظف المصرفية الحديثة مع تصميم متطور
class EmployeeScreen extends StatefulWidget {
  final OdooService odooService;
  final int uid;
  final String password;

  const EmployeeScreen({
    super.key,
    required this.odooService,
    required this.uid,
    required this.password,
  });

  @override
  State<EmployeeScreen> createState() => _EmployeeScreenState();
}

class _EmployeeScreenState extends State<EmployeeScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  Employee? _employee;
  bool _isLoading = true;
  String? _errorMessage;
  int _currentIndex = 0;
  bool _isLeaveManager = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    // تحديد عدد التبويبات بناءً على ما إذا كان المستخدم مدير إجازات
    _tabController = TabController(length: 3, vsync: this);
    _loadEmployeeData();

    // تتبع مشاهدة شاشة الموظف
    WidgetsBinding.instance.addPostFrameCallback((_) {
      FirebaseAnalyticsService.logScreenView(
        screenName: 'employee_screen',
        screenClass: 'EmployeeScreen',
      );
    });
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    _slideAnimation =
        Tween<Offset>(begin: const Offset(0, 0.2), end: Offset.zero).animate(
          CurvedAnimation(parent: _slideController, curve: Curves.easeOutCubic),
        );

    _fadeController.forward();
    _slideController.forward();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _fadeController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  /// تحميل بيانات الموظف من Odoo
  Future<void> _loadEmployeeData() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // تتبع أداء تحميل بيانات الموظف
      final employeeData =
          await PerformanceMonitoringService.traceDatabaseOperation(
            operationType: 'read',
            tableName: 'hr.employee',
            operation: () => widget.odooService.getCurrentEmployee(
              uid: widget.uid,
              password: widget.password,
            ),
          );

      // فحص ما إذا كان المستخدم مدير إجازات
      final isManager =
          await PerformanceMonitoringService.traceDatabaseOperation(
            operationType: 'check',
            tableName: 'hr.leave',
            operation: () => widget.odooService.isLeaveManager(
              uid: widget.uid,
              password: widget.password,
            ),
          );

      if (employeeData != null) {
        // تتبع نجاح تحميل بيانات الموظف
        await FirebaseAnalyticsService.logEvent(
          name: 'employee_data_loaded',
          parameters: {
            'is_manager': isManager ? 'true' : 'false',
            'has_employee_data': 'true',
          },
        );

        setState(() {
          _employee = Employee.fromOdooData(employeeData);
          _isLeaveManager = isManager;
          _isLoading = false;
        });

        // إعادة إنشاء TabController بالعدد الصحيح من التبويبات
        _tabController.dispose();
        _tabController = TabController(
          length: _isLeaveManager ? 4 : 3,
          vsync: this,
        );

        // إضافة listener لتتبع التنقل بين التبويبات
        _tabController.addListener(() {
          if (!_tabController.indexIsChanging) {
            _trackTabNavigation(_tabController.index);
          }
        });
      } else {
        // تتبع فشل تحميل بيانات الموظف
        await FirebaseAnalyticsService.logEvent(
          name: 'employee_data_load_failed',
          parameters: {'error_type': 'no_data'},
        );

        setState(() {
          _errorMessage = 'لم يتم العثور على بيانات الموظف';
          _isLoading = false;
        });
      }
    } catch (e) {
      // تتبع خطأ تحميل بيانات الموظف
      await FirebaseAnalyticsService.logError(
        errorType: 'employee_data_load_error',
        errorMessage: e.toString(),
        screenName: 'employee_screen',
        functionName: '_loadEmployeeData',
      );

      setState(() {
        _errorMessage = 'حدث خطأ في تحميل البيانات: $e';
        _isLoading = false;
      });
    }
  }

  /// تسجيل الخروج والعودة إلى شاشة تسجيل الدخول
  void _logout() {
    // تسجيل الخروج فوراً مع الاحتفاظ بالبيانات
    _logoutKeepData();
  }

  /// تسجيل الخروج مع الاحتفاظ بالبيانات
  void _logoutKeepData() async {
    // تتبع تسجيل الخروج
    await FirebaseAnalyticsService.logLogout(
      method: 'manual',
      dataCleared: false,
    );

    // إعادة تعيين بيانات Crashlytics
    await FirebaseCrashlyticsService.resetCrashlyticsData();

    if (mounted) {
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(builder: (context) => const LoginScreen()),
      );
    }
  }

  /// إعادة تحميل البيانات
  Future<void> _refreshData() async {
    // تتبع إعادة تحميل البيانات
    await FirebaseAnalyticsService.logEvent(
      name: 'data_refresh',
      parameters: {'screen': 'employee_screen'},
    );

    await _loadEmployeeData();
  }

  /// تتبع التنقل بين التبويبات
  void _trackTabNavigation(int tabIndex) {
    final tabNames = _isLeaveManager
        ? ['employee_info', 'leave_types', 'my_requests', 'leave_approval']
        : ['employee_info', 'leave_types', 'my_requests'];

    if (tabIndex < tabNames.length) {
      FirebaseAnalyticsService.logEvent(
        name: 'tab_navigation',
        parameters: {
          'tab_name': tabNames[tabIndex],
          'tab_index': tabIndex,
          'is_manager': _isLeaveManager ? 'true' : 'false',
        },
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConfig.lightGrayColorObj,
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: SlideTransition(
          position: _slideAnimation,
          child: Column(
            children: [
              _buildModernAppBar(),
              Expanded(
                child: _isLoading
                    ? _buildLoadingState()
                    : _errorMessage != null
                    ? _buildErrorState()
                    : _buildMainContent(),
              ),
            ],
          ),
        ),
      ),
      bottomNavigationBar: _buildBottomNavigation(),
    );
  }

  /// بناء شريط التطبيق الحديث
  Widget _buildModernAppBar() {
    return Container(
      padding: EdgeInsets.only(
        top: AppConfig.largeSpacing * 2,
        left: AppConfig.spacing,
        right: AppConfig.spacing,
        bottom: AppConfig.spacing,
      ),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [AppConfig.primaryColorObj, Color(0xFF0D519B)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(AppConfig.largeBorderRadius),
          bottomRight: Radius.circular(AppConfig.largeBorderRadius),
        ),
        boxShadow: [
          BoxShadow(
            color: AppConfig.primaryColorObj.withValues(alpha: 0.3),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          children: [
            // أيقونة الإعدادات
            GestureDetector(
              onTap: () => _navigateToSettings(),
              child: Container(
                padding: EdgeInsets.all(AppConfig.smallSpacing),
                decoration: BoxDecoration(
                  color: AppConfig.whiteColorObj.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(AppConfig.borderRadius),
                ),
                child: Icon(
                  Icons.settings,
                  color: AppConfig.whiteColorObj,
                  size: 28,
                ),
              ),
            ),
            SizedBox(width: AppConfig.spacing),

            // معلومات التطبيق
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    AppLocalizations.of(context).appName,
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      color: AppConfig.whiteColorObj,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    _employee?.name ?? 'مرحباً بك',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppConfig.whiteColorObj.withValues(alpha: 0.9),
                    ),
                  ),
                ],
              ),
            ),

            // أزرار الإجراءات
            Row(
              children: [
                _buildActionButton(
                  icon: Icons.refresh,
                  onPressed: _isLoading ? null : _refreshData,
                  tooltip: 'إعادة تحميل',
                ),
                SizedBox(width: AppConfig.smallSpacing),
                _buildActionButton(
                  icon: Icons.logout,
                  onPressed: _logout,
                  tooltip: 'تسجيل الخروج',
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء زر إجراء في شريط التطبيق
  Widget _buildActionButton({
    required IconData icon,
    required VoidCallback? onPressed,
    required String tooltip,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: AppConfig.whiteColorObj.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(AppConfig.borderRadius),
      ),
      child: IconButton(
        icon: Icon(icon, color: AppConfig.whiteColorObj, size: 22),
        onPressed: onPressed,
        tooltip: tooltip,
      ),
    );
  }

  /// بناء حالة التحميل
  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: EdgeInsets.all(AppConfig.largeSpacing),
            decoration: BoxDecoration(
              color: AppConfig.whiteColorObj,
              borderRadius: BorderRadius.circular(AppConfig.largeBorderRadius),
              boxShadow: [
                BoxShadow(
                  color: AppConfig.cardShadowColorObj,
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                ),
              ],
            ),
            child: Column(
              children: [
                CircularProgressIndicator(
                  color: AppConfig.primaryColorObj,
                  strokeWidth: 3,
                ),
                SizedBox(height: AppConfig.spacing),
                Text(
                  'جاري تحميل بيانات الموظف...',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: AppConfig.darkTextColorObj,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء حالة الخطأ
  Widget _buildErrorState() {
    return Center(
      child: Container(
        margin: EdgeInsets.all(AppConfig.largeSpacing),
        padding: EdgeInsets.all(AppConfig.largeSpacing),
        decoration: BoxDecoration(
          color: AppConfig.whiteColorObj,
          borderRadius: BorderRadius.circular(AppConfig.largeBorderRadius),
          boxShadow: [
            BoxShadow(
              color: AppConfig.cardShadowColorObj,
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: EdgeInsets.all(AppConfig.spacing),
              decoration: BoxDecoration(
                color: AppConfig.errorColorObj.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppConfig.borderRadius),
              ),
              child: Icon(
                Icons.error_outline,
                color: AppConfig.errorColorObj,
                size: 48,
              ),
            ),
            SizedBox(height: AppConfig.spacing),
            Text(
              'حدث خطأ',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: AppConfig.darkTextColorObj,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: AppConfig.smallSpacing),
            Text(
              _errorMessage!,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppConfig.secondaryTextColorObj,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: AppConfig.largeSpacing),
            ElevatedButton.icon(
              onPressed: _refreshData,
              icon: const Icon(Icons.refresh),
              label: Text(AppLocalizations.of(context).retry),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء المحتوى الرئيسي
  Widget _buildMainContent() {
    final children = [
      _buildEmployeeProfile(),
      LeaveTypesScreen(
        odooService: widget.odooService,
        uid: widget.uid,
        password: widget.password,
      ),
      MyLeaveRequestsScreen(
        odooService: widget.odooService,
        uid: widget.uid,
        password: widget.password,
      ),
    ];

    // إضافة شاشة الموافقة للمديرين فقط
    if (_isLeaveManager) {
      children.add(
        LeaveApprovalScreen(
          odooService: widget.odooService,
          uid: widget.uid,
          password: widget.password,
        ),
      );
    }

    return IndexedStack(index: _currentIndex, children: children);
  }

  /// بناء ملف الموظف الشخصي
  Widget _buildEmployeeProfile() {
    return RefreshIndicator(
      onRefresh: _refreshData,
      color: AppConfig.primaryColorObj,
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: EdgeInsets.all(AppConfig.spacing),
        child: Column(
          children: [
            _buildProfileCard(),
            SizedBox(height: AppConfig.spacing),
            _buildQuickActions(),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة الملف الشخصي
  Widget _buildProfileCard() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(AppConfig.largeSpacing),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [AppConfig.whiteColorObj, Color(0xFFF8FAFC)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(AppConfig.largeBorderRadius),
        boxShadow: [
          BoxShadow(
            color: AppConfig.cardShadowColorObj,
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        children: [
          // صورة الموظف
          _buildEmployeeAvatar(),
          SizedBox(height: AppConfig.spacing),

          // اسم الموظف
          Text(
            _employee?.name ?? 'غير محدد',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppConfig.darkTextColorObj,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: AppConfig.smallSpacing),

          // المنصب أو نوع الارتباط
          if (_employee?.connectionTypeText != null)
            Container(
              padding: EdgeInsets.symmetric(
                horizontal: AppConfig.spacing,
                vertical: AppConfig.smallSpacing,
              ),
              decoration: BoxDecoration(
                color: AppConfig.primaryColorObj.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppConfig.borderRadius),
              ),
              child: Text(
                _employee!.connectionTypeText!,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppConfig.primaryColorObj,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),

          SizedBox(height: AppConfig.largeSpacing),

          // معلومات إضافية
          _buildInfoRows(),
        ],
      ),
    );
  }

  /// بناء صفوف المعلومات
  Widget _buildInfoRows() {
    return Column(
      children: [
        if (_employee?.nationalNumber != null)
          _buildInfoRow(
            icon: Icons.credit_card,
            label: AppLocalizations.of(context).identification,
            value: _employee!.nationalNumber!,
            color: AppConfig.successColorObj,
          ),

        if (_employee?.managerName != null) ...[
          SizedBox(height: AppConfig.spacing),
          _buildInfoRow(
            icon: Icons.supervisor_account,
            label: AppLocalizations.of(context).directmanager,
            value: _employee!.managerName!,
            color: const Color(0xFFFF9500),
          ),
        ],
      ],
    );
  }

  /// بناء صف معلومة واحدة
  Widget _buildInfoRow({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Container(
      padding: EdgeInsets.all(AppConfig.spacing),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppConfig.borderRadius),
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(AppConfig.smallSpacing),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(AppConfig.smallSpacing),
            ),
            child: Icon(icon, color: color, size: 20),
          ),
          SizedBox(width: AppConfig.spacing),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppConfig.secondaryTextColorObj,
                    fontSize: AppConfig.smallFontSize,
                  ),
                ),
                Text(
                  value,
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: AppConfig.darkTextColorObj,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء الإجراءات السريعة
  Widget _buildQuickActions() {
    return Container(
      padding: EdgeInsets.all(AppConfig.spacing),
      decoration: BoxDecoration(
        color: AppConfig.whiteColorObj,
        borderRadius: BorderRadius.circular(AppConfig.largeBorderRadius),
        boxShadow: [
          BoxShadow(
            color: AppConfig.cardShadowColorObj,
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            AppLocalizations.of(context).quickactions,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppConfig.darkTextColorObj,
            ),
          ),
          SizedBox(height: AppConfig.spacing),
          Row(
            children: [
              Expanded(
                child: _buildQuickActionButton(
                  icon: Icons.event_available,
                  label: AppLocalizations.of(context).leaveTypes,
                  color: AppConfig.primaryColorObj,
                  onTap: () => setState(() => _currentIndex = 1),
                ),
              ),
              SizedBox(width: AppConfig.spacing),
              Expanded(
                child: _buildQuickActionButton(
                  icon: Icons.history,
                  label: AppLocalizations.of(context).myLeaveRequests,
                  color: AppConfig.successColorObj,
                  onTap: () => setState(() => _currentIndex = 2),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء زر إجراء سريع
  Widget _buildQuickActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(AppConfig.spacing),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              color.withValues(alpha: 0.1),
              color.withValues(alpha: 0.05),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(AppConfig.borderRadius),
          border: Border.all(color: color.withValues(alpha: 0.2)),
        ),
        child: Column(
          children: [
            Container(
              padding: EdgeInsets.all(AppConfig.spacing),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(AppConfig.borderRadius),
              ),
              child: Icon(icon, color: color, size: 28),
            ),
            SizedBox(height: AppConfig.smallSpacing),
            Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: color,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// بناء شريط التنقل السفلي
  Widget _buildBottomNavigation() {
    return Container(
      decoration: BoxDecoration(
        color: AppConfig.whiteColorObj,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(AppConfig.largeBorderRadius),
          topRight: Radius.circular(AppConfig.largeBorderRadius),
        ),
        boxShadow: [
          BoxShadow(
            color: AppConfig.cardShadowColorObj,
            blurRadius: 20,
            offset: const Offset(0, -10),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(AppConfig.largeBorderRadius),
          topRight: Radius.circular(AppConfig.largeBorderRadius),
        ),
        child: BottomNavigationBar(
          currentIndex: _currentIndex,
          onTap: _onTabTapped,
          type: BottomNavigationBarType.fixed,
          backgroundColor: AppConfig.whiteColorObj,
          selectedItemColor: AppConfig.primaryColorObj,
          unselectedItemColor: AppConfig.secondaryTextColorObj,
          selectedLabelStyle: TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: AppConfig.smallFontSize,
          ),
          unselectedLabelStyle: TextStyle(
            fontWeight: FontWeight.normal,
            fontSize: AppConfig.smallFontSize,
          ),
          items: _buildNavigationItems(),
        ),
      ),
    );
  }

  /// معالجة النقر على التبويبات
  void _onTabTapped(int index) {
    // التأكد من أن الفهرس صحيح
    final maxIndex = _isLeaveManager ? 3 : 2;
    if (index > maxIndex) return;

    setState(() => _currentIndex = index);
  }

  /// الانتقال إلى شاشة الإعدادات
  void _navigateToSettings() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const SettingsScreen()),
    );
  }

  /// بناء عناصر التنقل السفلي
  List<BottomNavigationBarItem> _buildNavigationItems() {
    final items = [
      BottomNavigationBarItem(
        icon: Icon(Icons.person),
        label: AppLocalizations.of(context).myprofile,
      ),
      BottomNavigationBarItem(
        icon: Icon(Icons.event_available),
        label: AppLocalizations.of(context).leaveTypes,
      ),
      BottomNavigationBarItem(
        icon: Icon(Icons.history),
        label: AppLocalizations.of(context).myLeaveRequests,
      ),
    ];

    // إضافة تبويب الموافقة للمديرين فقط
    if (_isLeaveManager) {
      items.add(
        BottomNavigationBarItem(
          icon: Icon(Icons.approval),
          label: AppLocalizations.of(context).leaveApprovals,
        ),
      );
    }

    return items;
  }

  /// بناء صورة الموظف المحسنة
  Widget _buildEmployeeAvatar() {
    return OptimizedEmployeeImage(
      base64Data: _employee?.image1920,
      width: 100,
      height: 100,
      fit: BoxFit.cover,
      borderRadius: BorderRadius.circular(50),
      boxShadow: [
        BoxShadow(
          color: AppConfig.primaryColorObj.withValues(alpha: 0.3),
          blurRadius: 20,
          offset: const Offset(0, 10),
        ),
      ],
      placeholder: _buildLoadingAvatar(),
      errorWidget: _buildDefaultAvatar(),
      enableOptimization: true,
    );
  }

  /// بناء مؤشر التحميل للصورة
  Widget _buildLoadingAvatar() {
    return Container(
      width: 100,
      height: 100,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.grey[300]!, Colors.grey[100]!],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(50),
      ),
      child: Center(
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(AppConfig.primaryColorObj),
        ),
      ),
    );
  }

  /// بناء الصورة الافتراضية
  Widget _buildDefaultAvatar() {
    return Container(
      width: 100,
      height: 100,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [AppConfig.primaryColorObj, Color(0xFF2563EB)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(50),
      ),
      child: Icon(Icons.person, size: 50, color: AppConfig.whiteColorObj),
    );
  }
}
