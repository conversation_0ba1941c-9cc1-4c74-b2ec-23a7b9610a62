import 'dart:convert';
import 'dart:ui' as ui;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:crypto/crypto.dart';

/// خدمة تحسين تحميل وتخزين الصور
class ImageCacheService {
  static final ImageCacheService _instance = ImageCacheService._internal();
  factory ImageCacheService() => _instance;
  ImageCacheService._internal();

  // تخزين مؤقت للصور في الذاكرة
  final Map<String, Uint8List> _memoryCache = {};
  final Map<String, ui.Image> _decodedImageCache = {};

  // حد أقصى لحجم التخزين المؤقت (10 صور)
  static const int _maxCacheSize = 10;

  // قائمة انتظار لفك تشفير الصور
  final Map<String, Future<Uint8List?>> _decodingQueue = {};

  /// تنظيف التخزين المؤقت عند امتلائه
  void _cleanupCache() {
    if (_memoryCache.length > _maxCacheSize) {
      // إزالة أقدم العناصر
      final keysToRemove = _memoryCache.keys.take(
        _memoryCache.length - _maxCacheSize,
      );
      for (final key in keysToRemove) {
        _memoryCache.remove(key);
        _decodedImageCache.remove(key);
      }
    }
  }

  /// إنشاء مفتاح فريد للصورة
  String _generateCacheKey(String base64Data) {
    final bytes = utf8.encode(base64Data);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// فك تشفير الصورة بشكل غير متزامن
  Future<Uint8List?> _decodeBase64Async(String base64Data) async {
    try {
      // فك التشفير في خيط منفصل لتجنب تجميد الواجهة
      return await compute(_decodeBase64, base64Data);
    } catch (e) {
      debugPrint('خطأ في فك تشفير الصورة: $e');
      return null;
    }
  }

  /// دالة فك التشفير (تعمل في خيط منفصل)
  static Uint8List? _decodeBase64(String base64Data) {
    try {
      return base64Decode(base64Data);
    } catch (e) {
      return null;
    }
  }

  /// تحسين حجم الصورة
  Future<Uint8List?> _optimizeImage(
    Uint8List imageBytes, {
    int? maxWidth,
    int? maxHeight,
  }) async {
    try {
      final codec = await ui.instantiateImageCodec(
        imageBytes,
        targetWidth: maxWidth,
        targetHeight: maxHeight,
      );
      final frame = await codec.getNextFrame();
      final image = frame.image;

      // تحويل الصورة إلى bytes محسنة
      final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      if (byteData != null) {
        return byteData.buffer.asUint8List();
      }
    } catch (e) {
      debugPrint('خطأ في تحسين الصورة: $e');
    }
    return imageBytes; // إرجاع الصورة الأصلية في حالة الفشل
  }

  /// جلب الصورة مع التخزين المؤقت
  Future<Uint8List?> getImage(
    String base64Data, {
    int? maxWidth,
    int? maxHeight,
  }) async {
    if (base64Data.isEmpty) return null;

    final cacheKey = _generateCacheKey(base64Data);

    // التحقق من وجود الصورة في التخزين المؤقت
    if (_memoryCache.containsKey(cacheKey)) {
      return _memoryCache[cacheKey];
    }

    // التحقق من وجود عملية فك تشفير جارية
    if (_decodingQueue.containsKey(cacheKey)) {
      return await _decodingQueue[cacheKey];
    }

    // بدء عملية فك التشفير
    final decodingFuture = _processImage(
      base64Data,
      cacheKey,
      maxWidth: maxWidth,
      maxHeight: maxHeight,
    );
    _decodingQueue[cacheKey] = decodingFuture;

    final result = await decodingFuture;
    _decodingQueue.remove(cacheKey);

    return result;
  }

  /// معالجة الصورة (فك التشفير + التحسين + التخزين)
  Future<Uint8List?> _processImage(
    String base64Data,
    String cacheKey, {
    int? maxWidth,
    int? maxHeight,
  }) async {
    try {
      // فك التشفير
      final imageBytes = await _decodeBase64Async(base64Data);
      if (imageBytes == null) return null;

      // تحسين الحجم إذا كان مطلوباً
      final optimizedBytes = maxWidth != null || maxHeight != null
          ? await _optimizeImage(
              imageBytes,
              maxWidth: maxWidth,
              maxHeight: maxHeight,
            )
          : imageBytes;

      if (optimizedBytes != null) {
        // حفظ في التخزين المؤقت
        _memoryCache[cacheKey] = optimizedBytes;
        _cleanupCache();
        return optimizedBytes;
      }
    } catch (e) {
      debugPrint('خطأ في معالجة الصورة: $e');
    }
    return null;
  }

  /// تنظيف التخزين المؤقت
  void clearCache() {
    _memoryCache.clear();
    _decodedImageCache.clear();
    _decodingQueue.clear();
  }

  /// الحصول على حجم التخزين المؤقت
  int getCacheSize() => _memoryCache.length;

  /// التحقق من وجود صورة في التخزين المؤقت
  bool isImageCached(String base64Data) {
    final cacheKey = _generateCacheKey(base64Data);
    return _memoryCache.containsKey(cacheKey);
  }

  /// إحصائيات التخزين المؤقت
  Map<String, dynamic> getCacheStats() {
    int totalSize = 0;
    for (final bytes in _memoryCache.values) {
      totalSize += bytes.length;
    }

    return {
      'cached_images': _memoryCache.length,
      'total_size_bytes': totalSize,
      'total_size_mb': (totalSize / (1024 * 1024)).toStringAsFixed(2),
      'decoding_queue': _decodingQueue.length,
    };
  }
}

/// ويدجت محسن لعرض صور الموظفين
class OptimizedEmployeeImage extends StatefulWidget {
  final String? base64Data;
  final double width;
  final double height;
  final Widget? placeholder;
  final Widget? errorWidget;
  final BoxFit fit;
  final BorderRadius? borderRadius;
  final List<BoxShadow>? boxShadow;
  final bool enableOptimization;

  const OptimizedEmployeeImage({
    super.key,
    this.base64Data,
    required this.width,
    required this.height,
    this.placeholder,
    this.errorWidget,
    this.fit = BoxFit.cover,
    this.borderRadius,
    this.boxShadow,
    this.enableOptimization = true,
  });

  @override
  State<OptimizedEmployeeImage> createState() => _OptimizedEmployeeImageState();
}

class _OptimizedEmployeeImageState extends State<OptimizedEmployeeImage> {
  final ImageCacheService _cacheService = ImageCacheService();
  Uint8List? _imageBytes;
  bool _isLoading = false;
  bool _hasError = false;

  @override
  void initState() {
    super.initState();
    _loadImage();
  }

  @override
  void didUpdateWidget(OptimizedEmployeeImage oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.base64Data != widget.base64Data) {
      _loadImage();
    }
  }

  Future<void> _loadImage() async {
    if (widget.base64Data == null || widget.base64Data!.isEmpty) {
      setState(() {
        _imageBytes = null;
        _isLoading = false;
        _hasError = false;
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _hasError = false;
    });

    try {
      final imageBytes = await _cacheService.getImage(
        widget.base64Data!,
        maxWidth: widget.enableOptimization ? widget.width.toInt() * 2 : null,
        maxHeight: widget.enableOptimization ? widget.height.toInt() * 2 : null,
      );

      if (mounted) {
        setState(() {
          _imageBytes = imageBytes;
          _isLoading = false;
          _hasError = imageBytes == null;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _hasError = true;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    Widget child;

    if (_isLoading) {
      child = widget.placeholder ?? _buildLoadingPlaceholder();
    } else if (_hasError || _imageBytes == null) {
      child = widget.errorWidget ?? _buildErrorWidget();
    } else {
      child = Image.memory(
        _imageBytes!,
        width: widget.width,
        height: widget.height,
        fit: widget.fit,
        errorBuilder: (context, error, stackTrace) {
          return widget.errorWidget ?? _buildErrorWidget();
        },
      );
    }

    return Container(
      width: widget.width,
      height: widget.height,
      decoration: BoxDecoration(
        borderRadius: widget.borderRadius,
        boxShadow: widget.boxShadow,
      ),
      child: ClipRRect(
        borderRadius: widget.borderRadius ?? BorderRadius.zero,
        child: child,
      ),
    );
  }

  Widget _buildLoadingPlaceholder() {
    return Container(
      width: widget.width,
      height: widget.height,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.grey[300]!, Colors.grey[100]!],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: const Center(child: CircularProgressIndicator(strokeWidth: 2)),
    );
  }

  Widget _buildErrorWidget() {
    return Container(
      width: widget.width,
      height: widget.height,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.blue[600]!, Colors.blue[800]!],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: Icon(Icons.person, size: widget.width * 0.5, color: Colors.white),
    );
  }
}
