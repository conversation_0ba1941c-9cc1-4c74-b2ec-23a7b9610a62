# HR Leave Notifications - Odoo Module

## 📱 **الوصف**
هذا الموديول يضيف إشعارات Firebase Cloud Messaging لإدارة الإجازات في Odoo 15.

## ✅ **الميزات**
- إشعار الموظف عند قبول/رفض طلب الإجازة
- إشعار المدير عند تقديم طلب إجازة جديد
- حفظ FCM tokens للمستخدمين
- API methods للتطبيق المحمول

## 🔧 **التثبيت**

### 1. نسخ الملفات:
```bash
cp -r hr_leave_notifications /path/to/odoo/addons/
```

### 2. إعادة تشغيل Odoo:
```bash
sudo systemctl restart odoo
```

### 3. تفعيل الموديول:
- اذهب إلى Apps
- ابحث عن "HR Leave Notifications"
- اضغط Install

## 🚀 **الاستخدام**

### API Endpoints المتاحة:

#### 1. حفظ FCM Token:
```python
# من التطبيق المحمول
self.env['res.users'].save_fcm_token(
    user_id=user_id,
    fcm_token="fcm_token_here",
    platform="android"
)
```

#### 2. الموافقة على الإجازة:
```python
self.env['hr.leave'].approve_leave_with_notification(leave_id)
```

#### 3. رفض الإجازة:
```python
self.env['hr.leave'].reject_leave_with_notification(
    leave_id=leave_id,
    reason="السبب هنا"
)
```

## 📊 **الإشعارات المرسلة**

### للموظف:
- ✅ **قبول الإجازة**: "تم قبول طلبك للإجازة من X إلى Y"
- ❌ **رفض الإجازة**: "تم رفض طلبك للإجازة من X إلى Y"

### للمدير:
- 📋 **طلب جديد**: "طلب إجازة جديد من [اسم الموظف]"

## 🔑 **Firebase Server Key**
الموديول يستخدم Server Key المحدد في الكود:
`BJgQEGNh9Xx0790bgYDnd7LcY9wvL--k0xZNWg2ek5wjkGZXnrDJymu7NONN6UFDDCStQKWjJW9Kdkba3hNA6iI`

## 📝 **ملاحظات**
- يتطلب مكتبة `requests` في Python
- يعمل مع Odoo 15
- متوافق مع التطبيق المحمول Flutter

## 🎯 **النتيجة**
إشعارات فورية وتلقائية لجميع عمليات الإجازات! 🚀
