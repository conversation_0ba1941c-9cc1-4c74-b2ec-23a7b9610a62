# تثبيت Firebase Admin SDK في Odoo

## 🔧 **خطوات التثبيت:**

### 1. **تثبيت المكتبة:**
```bash
# في خادم Odoo
pip install firebase-admin

# أو إذا كنت تستخدم virtual environment
source /path/to/odoo/venv/bin/activate
pip install firebase-admin
```

### 2. **إعادة تشغيل Odoo:**
```bash
sudo systemctl restart odoo
```

### 3. **التحقق من التثبيت:**
- ثبت الموديول `hr_leave_notifications`
- تحقق من Odoo logs، يجب أن ترى:
  ```
  Firebase Admin SDK initialized successfully
  ```

## 🚀 **بعد التثبيت:**

### **الإشعارات ستعمل تلقائياً:**
- ✅ **قبول الإجازة** → إشعار للموظف
- ❌ **رفض الإجازة** → إشعار للموظف مع السبب  
- 📋 **طلب جديد** → إشعار للمدير

### **في Odoo logs ستجد:**
```
Firebase Admin FCM notification sent successfully to [اسم المستخدم]: projects/odoo-employee-app/messages/0:1234567890
```

## ⚠️ **إذا لم تثبت المكتبة:**
- الموديول سيعمل بدون أخطاء
- سيستخدم الطريقة البديلة (تسجيل في logs)
- ستجد في logs:
  ```
  Firebase Admin SDK not available. Install with: pip install firebase-admin
  === FCM NOTIFICATION ===
  To: اسم المستخدم
  Title: تم قبول طلب الإجازة
  ========================
  ```

## 🎯 **النتيجة:**
مع Firebase Admin SDK، الإشعارات ستصل فعلياً للتطبيق المحمول! 🚀

## 📝 **ملاحظة:**
Service Account JSON مدمج في الكود، لا حاجة لملفات إضافية.
